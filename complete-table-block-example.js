/**
 * 完整的 NocoBase 表格区块配置示例
 * 基于 Playwright 观察到的成功配置模式
 */

const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');
const { uid } = require('./mcp-server-nocobase/dist/utils.js');

/**
 * 创建完整的表格区块 Schema
 * 包含：表格操作、列配置、行操作等完整功能
 */
function createCompleteTableBlockSchema(options) {
  const {
    collectionName,
    dataSource = 'main',
    title,
    columns = [],
    tableActions = [],
    rowActions = [],
    params = {}
  } = options;

  const blockUid = uid();
  const tableUid = uid();
  const actionsUid = uid();

  // 基础表格 Schema
  const tableSchema = {
    type: 'void',
    name: blockUid,
    'x-uid': blockUid,
    'x-acl-action': `${collectionName}:list`,
    'x-decorator': 'TableBlockProvider',
    'x-decorator-props': {
      collection: collectionName,
      dataSource,
      action: 'list',
      params: {
        pageSize: 20,
        ...params
      },
      showIndex: true,
      dragSort: false,
      rowKey: 'id'
    },
    'x-toolbar': 'BlockSchemaToolbar',
    'x-settings': 'blockSettings:table',
    'x-component': 'CardItem',
    'x-component-props': {
      title: title || `{{t("${collectionName}")}}`,
    },
    properties: {
      // 表格级别的操作栏
      actions: {
        type: 'void',
        'x-uid': actionsUid,
        'x-initializer': 'table:configureActions',
        'x-component': 'ActionBar',
        'x-component-props': {
          style: {
            marginBottom: 'var(--nb-spacing)',
          },
        },
        properties: {}
      },
      // 表格主体
      [tableUid]: {
        type: 'array',
        'x-uid': tableUid,
        'x-initializer': 'table:configureColumns',
        'x-component': 'TableV2',
        'x-use-component-props': 'useTableBlockProps',
        'x-component-props': {
          rowKey: 'id',
          rowSelection: {
            type: 'checkbox',
          },
        },
        properties: {}
      }
    }
  };

  // 添加表格级别的操作
  if (tableActions.length > 0) {
    tableActions.forEach(action => {
      const actionUid = uid();
      tableSchema.properties.actions.properties[actionUid] = createTableAction(action, collectionName);
    });
  }

  // 添加 Actions 列（行操作）
  if (rowActions.length > 0) {
    const actionsColumnUid = uid();
    tableSchema.properties[tableUid].properties[actionsColumnUid] = createActionsColumn(rowActions, collectionName);
  }

  // 添加数据列
  if (columns.length > 0) {
    columns.forEach(column => {
      const columnUid = uid();
      tableSchema.properties[tableUid].properties[columnUid] = createDataColumn(column, collectionName);
    });
  }

  return tableSchema;
}

/**
 * 创建表格级别的操作
 */
function createTableAction(action, collectionName) {
  const actionUid = uid();
  
  const actionSchemas = {
    addNew: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'create',
      'x-decorator': 'ACLActionProvider',
      'x-component': 'Action',
      'x-toolbar': 'ActionSchemaToolbar',
      'x-settings': 'actionSettings:addNew',
      'x-component-props': {
        openMode: 'drawer',
        type: 'primary',
        icon: 'PlusOutlined',
        ...action.props
      },
      title: action.title || '{{t("Add new")}}',
      properties: {
        drawer: {
          type: 'void',
          title: action.title || '{{t("Add new")}}',
          'x-component': 'Action.Container',
          'x-component-props': {
            className: 'nb-action-popup',
          },
          properties: {
            tabs: {
              type: 'void',
              'x-component': 'Tabs',
              'x-component-props': {},
              'x-initializer': 'TabPaneInitializers',
              properties: {
                tab1: {
                  type: 'void',
                  title: '{{t("Add new")}}',
                  'x-component': 'Tabs.TabPane',
                  'x-designer': 'Tabs.Designer',
                  'x-component-props': {},
                  properties: {
                    grid: {
                      type: 'void',
                      'x-component': 'Grid',
                      'x-initializer': 'popup:addNew:addBlock',
                      properties: {},
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    filter: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'filter',
      'x-decorator': 'ACLActionProvider',
      'x-component': 'Filter.Action',
      'x-use-component-props': 'useFilterActionProps',
      'x-component-props': {
        icon: 'FilterOutlined',
        useProps: '{{ useFilterActionProps }}',
        ...action.props
      },
      'x-align': 'left',
      title: action.title || '{{t("Filter")}}'
    },
    refresh: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'refresh',
      'x-component': 'Action',
      'x-toolbar': 'ActionSchemaToolbar',
      'x-settings': 'actionSettings:refresh',
      'x-component-props': {
        icon: 'ReloadOutlined',
        useProps: '{{ useRefreshActionProps }}',
        ...action.props
      },
      'x-use-component-props': 'useRefreshActionProps',
      title: action.title || '{{t("Refresh")}}'
    }
  };

  return actionSchemas[action.type] || actionSchemas.addNew;
}

/**
 * 创建 Actions 列（行操作列）
 */
function createActionsColumn(rowActions, collectionName) {
  const columnUid = uid();
  
  const actionsColumn = {
    type: 'void',
    'x-uid': columnUid,
    'x-decorator': 'TableV2.Column.ActionBar',
    'x-component': 'TableV2.Column',
    'x-designer': 'TableV2.ActionColumnDesigner',
    'x-initializer': 'table:configureItemActions',
    'x-component-props': {
      width: 120,
      fixed: 'right'
    },
    title: '{{t("Actions")}}',
    properties: {}
  };

  // 添加行操作
  rowActions.forEach(action => {
    const actionUid = uid();
    actionsColumn.properties[actionUid] = createRowAction(action, collectionName);
  });

  return actionsColumn;
}

/**
 * 创建行操作
 */
function createRowAction(action, collectionName) {
  const actionUid = uid();
  
  const actionSchemas = {
    view: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'view',
      'x-decorator': 'ACLActionProvider',
      'x-component': 'Action.Link',
      'x-component-props': {
        openMode: 'drawer',
        danger: false,
        ...action.props
      },
      'x-designer': 'Action.Designer',
      'x-toolbar': 'ActionSchemaToolbar',
      'x-settings': 'actionSettings:view',
      title: action.title || '{{t("View")}}',
      properties: {
        drawer: {
          type: 'void',
          title: action.title || '{{t("View record")}}',
          'x-component': 'Action.Container',
          'x-component-props': {
            className: 'nb-action-popup',
          },
          properties: {
            tabs: {
              type: 'void',
              'x-component': 'Tabs',
              'x-component-props': {},
              'x-initializer': 'TabPaneInitializers',
              properties: {
                tab1: {
                  type: 'void',
                  title: '{{t("Details")}}',
                  'x-component': 'Tabs.TabPane',
                  'x-designer': 'Tabs.Designer',
                  'x-component-props': {},
                  properties: {
                    grid: {
                      type: 'void',
                      'x-component': 'Grid',
                      'x-initializer': 'popup:common:addBlock',
                      properties: {},
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    edit: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'update',
      'x-decorator': 'ACLActionProvider',
      'x-component': 'Action.Link',
      'x-component-props': {
        openMode: 'drawer',
        icon: 'EditOutlined',
        danger: false,
        ...action.props
      },
      'x-designer': 'Action.Designer',
      'x-toolbar': 'ActionSchemaToolbar',
      'x-settings': 'actionSettings:edit',
      title: action.title || '{{t("Edit")}}',
      properties: {
        drawer: {
          type: 'void',
          title: action.title || '{{t("Edit record")}}',
          'x-component': 'Action.Container',
          'x-component-props': {
            className: 'nb-action-popup',
          },
          properties: {
            tabs: {
              type: 'void',
              'x-component': 'Tabs',
              'x-component-props': {},
              'x-initializer': 'TabPaneInitializers',
              properties: {
                tab1: {
                  type: 'void',
                  title: '{{t("Edit")}}',
                  'x-component': 'Tabs.TabPane',
                  'x-designer': 'Tabs.Designer',
                  'x-component-props': {},
                  properties: {
                    grid: {
                      type: 'void',
                      'x-component': 'Grid',
                      'x-initializer': 'popup:addRecord:addBlock',
                      properties: {},
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
    delete: {
      type: 'void',
      'x-uid': actionUid,
      'x-action': 'destroy',
      'x-decorator': 'ACLActionProvider',
      'x-component': 'Action.Link',
      'x-component-props': {
        icon: 'DeleteOutlined',
        confirm: {
          title: "{{t('Delete record')}}",
          content: "{{t('Are you sure you want to delete it?')}}",
        },
        useProps: '{{ useDestroyActionProps }}',
        danger: true,
        ...action.props
      },
      'x-designer': 'Action.Designer',
      'x-toolbar': 'ActionSchemaToolbar',
      'x-settings': 'actionSettings:delete',
      'x-use-component-props': 'useDestroyActionProps',
      title: action.title || '{{t("Delete")}}'
    }
  };

  return actionSchemas[action.type] || actionSchemas.view;
}

/**
 * 创建数据列
 */
function createDataColumn(column, collectionName) {
  const columnUid = uid();
  
  return {
    type: 'void',
    'x-uid': columnUid,
    'x-decorator': 'TableV2.Column.Decorator',
    'x-component': 'TableV2.Column',
    'x-designer': 'TableV2.Column.Designer',
    'x-component-props': {
      width: column.width || 200,
      ...column.props
    },
    title: column.title || `{{t("${column.field}")}}`,
    properties: {
      [column.field]: {
        type: 'string',
        'x-uid': uid(),
        'x-collection-field': `${collectionName}.${column.field}`,
        'x-component': column.component || 'CollectionField',
        'x-component-props': {
          ...column.componentProps
        },
        'x-read-pretty': true,
        'x-decorator': null,
        'x-decorator-props': {
          labelStyle: {
            display: 'none',
          },
        },
      },
    },
  };
}

/**
 * 测试完整表格配置的示例
 */
async function testCompleteTableBlock() {
  console.log('🧪 Testing complete table block configuration...\n');

  // 初始化客户端
  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    const parentUid = 'tab-1754717531467-kt42byc12'; // 页面的 tab UID

    // 创建完整的表格配置
    const completeTableSchema = createCompleteTableBlockSchema({
      collectionName: 'towns',
      dataSource: 'main',
      title: '镇街管理表格（完整版）',
      // 表格级别的操作
      tableActions: [
        { type: 'addNew', title: '添加镇街' },
        { type: 'filter', title: '筛选' },
        { type: 'refresh', title: '刷新' }
      ],
      // 数据列配置
      columns: [
        { field: 'name', title: '镇街名称', width: 200 },
        { field: 'city', title: '城市', width: 150 },
        { field: 'district', title: '区县', width: 150 },
        { field: 'createdAt', title: '创建时间', width: 180, component: 'DatePicker' }
      ],
      // 行操作配置
      rowActions: [
        { type: 'view', title: '查看详情' },
        { type: 'edit', title: '编辑' },
        { type: 'delete', title: '删除' }
      ],
      // 查询参数
      params: {
        pageSize: 10,
        sort: ['-createdAt']
      }
    });

    console.log('📝 Complete Table Schema Generated:');
    console.log(JSON.stringify(completeTableSchema, null, 2));
    console.log('');

    // 创建表格区块
    const result = await client.insertBlockToGrid(parentUid, completeTableSchema, 'beforeEnd');
    console.log('✅ Complete table block created successfully!');
    console.log('📊 Result:', JSON.stringify(result, null, 2));

  } catch (error) {
    console.error('❌ Complete table block creation failed:', error.message);
    console.error('📋 Error details:', error.response?.data || error);
  }
}

module.exports = {
  createCompleteTableBlockSchema,
  createTableAction,
  createActionsColumn,
  createRowAction,
  createDataColumn,
  testCompleteTableBlock
};

// 如果直接运行此文件，执行测试
if (require.main === module) {
  testCompleteTableBlock().catch(console.error);
}

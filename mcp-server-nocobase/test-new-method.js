#!/usr/bin/env node

// 测试新的 createCollectionWithDefaults 方法
import { NocoBaseClient } from './dist/client.js';

async function testNewMethod() {
  console.log('🧪 测试新的 createCollectionWithDefaults 方法...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 删除已存在的测试集合（如果存在）
    try {
      await client.deleteCollection('test_villages');
      console.log('ℹ️  删除已存在的测试集合');
    } catch (error) {
      // 集合不存在，忽略错误
    }

    // 使用新方法创建集合
    console.log('📋 使用 createCollectionWithDefaults 创建村庄集合');
    const collection = await client.createCollectionWithDefaults({
      name: 'test_villages',
      title: '测试村庄集合',
      description: '测试用村庄信息集合',
      autoGenId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: true,
      updatedBy: true,
      fields: [
        {
          name: 'province',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '省份',
            'x-component': 'Input',
            required: true
          }
        },
        {
          name: 'city',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '城市',
            'x-component': 'Input',
            required: true
          }
        },
        {
          name: 'district',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '区县',
            'x-component': 'Input',
            required: true
          }
        },
        {
          name: 'village',
          type: 'string',
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '村庄',
            'x-component': 'Input',
            required: true
          }
        }
      ]
    });

    console.log(`✅ 成功创建村庄集合: ${collection.name} (${collection.title})`);

    // 验证字段
    console.log('\n🔍 验证创建的字段:');
    const fields = await client.listFields('test_villages');
    console.log(`✅ 共有 ${fields.length} 个字段:`);
    
    const expectedFields = ['id', 'createdAt', 'updatedAt', 'createdBy', 'updatedBy', 'province', 'city', 'district', 'village'];
    const actualFields = fields.map(f => f.name);
    
    console.log('预期字段:', expectedFields);
    console.log('实际字段:', actualFields);
    
    const missingFields = expectedFields.filter(f => !actualFields.includes(f));
    const extraFields = actualFields.filter(f => !expectedFields.includes(f));
    
    if (missingFields.length === 0 && extraFields.length === 0) {
      console.log('✅ 所有字段都正确创建！');
    } else {
      if (missingFields.length > 0) {
        console.log('❌ 缺失字段:', missingFields);
      }
      if (extraFields.length > 0) {
        console.log('⚠️  额外字段:', extraFields);
      }
    }

    console.log('\n字段详情:');
    fields.forEach(field => {
      console.log(`   • ${field.name} (${field.type}) - ${field.interface || 'no interface'} - ${field.uiSchema?.title || '无标题'}`);
    });

    // 创建测试数据
    console.log('\n📋 创建测试数据:');
    const record = await client.createRecord('test_villages', {
      province: '广东省',
      city: '广州市',
      district: '从化区',
      village: '温泉镇'
    });
    console.log(`✅ 创建测试记录: ${record.province} ${record.city} ${record.district} ${record.village}`);

    // 检查记录的完整数据
    console.log('\n🔍 检查记录的完整数据:');
    const records = await client.listRecords('test_villages', { pageSize: 1 });
    if (records.data.length > 0) {
      const record = records.data[0];
      console.log('记录中的所有字段:');
      Object.keys(record).forEach(key => {
        console.log(`   ${key}: ${record[key]}`);
      });
    }

    console.log('\n🎉 新方法测试成功！所有默认字段都正确创建。');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
  }
}

testNewMethod().catch(console.error);

#!/usr/bin/env node

import { NocoBaseClient } from './dist/client.js';

// 使用开发环境配置
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground',
});

async function queryTownsCollections() {
  try {
    console.log('正在查询所有集合...');
    
    // 获取所有集合
    const collections = await client.get('/collections:list');
    console.log('找到的集合数量:', collections.data?.length || 0);
    
    // 查找镇街相关的集合
    const townRelatedCollections = collections.data?.filter(collection => {
      const name = collection.name?.toLowerCase() || '';
      const title = collection.title?.toLowerCase() || '';
      return name.includes('town') || name.includes('镇') || name.includes('街') || 
             title.includes('town') || title.includes('镇') || title.includes('街') ||
             name.includes('township') || name.includes('district');
    }) || [];
    
    console.log('\n镇街相关的集合:');
    if (townRelatedCollections.length > 0) {
      townRelatedCollections.forEach(collection => {
        console.log(`- ${collection.name} (${collection.title || '无标题'})`);
        console.log(`  描述: ${collection.description || '无描述'}`);
        console.log(`  字段数: ${collection.fields?.length || 0}`);
        console.log('');
      });
    } else {
      console.log('未找到镇街相关的集合');
    }
    
    // 显示所有集合名称以供参考
    console.log('\n所有集合列表:');
    collections.data?.forEach(collection => {
      console.log(`- ${collection.name} (${collection.title || '无标题'})`);
    });
    
  } catch (error) {
    console.error('查询失败:', error.message);
    if (error.response?.data) {
      console.error('错误详情:', error.response.data);
    }
  }
}

async function createTownsCollection() {
  try {
    console.log('\n正在创建镇街集合...');
    
    const collectionData = {
      name: 'towns',
      title: '镇街管理',
      description: '镇街基础信息管理',
      fields: [
        {
          name: 'id',
          type: 'bigInt',
          primaryKey: true,
          autoIncrement: true,
          interface: 'id',
          uiSchema: {
            type: 'number',
            title: 'ID',
            'x-component': 'InputNumber',
            'x-read-pretty': true,
          },
        },
        {
          name: 'name',
          type: 'string',
          allowNull: false,
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '镇街名称',
            'x-component': 'Input',
            required: true,
          },
        },
        {
          name: 'code',
          type: 'string',
          unique: true,
          interface: 'input',
          uiSchema: {
            type: 'string',
            title: '镇街代码',
            'x-component': 'Input',
            description: '唯一标识代码',
          },
        },
        {
          name: 'type',
          type: 'string',
          interface: 'select',
          uiSchema: {
            type: 'string',
            title: '类型',
            'x-component': 'Select',
            enum: [
              { label: '镇', value: 'town' },
              { label: '街道', value: 'street' },
              { label: '乡', value: 'township' },
            ],
          },
        },
        {
          name: 'level',
          type: 'string',
          interface: 'select',
          uiSchema: {
            type: 'string',
            title: '行政级别',
            'x-component': 'Select',
            enum: [
              { label: '县级', value: 'county' },
              { label: '地级', value: 'prefecture' },
              { label: '省级', value: 'province' },
            ],
          },
        },
        {
          name: 'parentId',
          type: 'bigInt',
          interface: 'select',
          uiSchema: {
            type: 'number',
            title: '上级行政区',
            'x-component': 'Select',
            description: '所属上级行政区域',
          },
        },
        {
          name: 'address',
          type: 'text',
          interface: 'textarea',
          uiSchema: {
            type: 'string',
            title: '详细地址',
            'x-component': 'Input.TextArea',
          },
        },
        {
          name: 'population',
          type: 'integer',
          interface: 'number',
          uiSchema: {
            type: 'number',
            title: '人口数量',
            'x-component': 'InputNumber',
          },
        },
        {
          name: 'area',
          type: 'double',
          interface: 'number',
          uiSchema: {
            type: 'number',
            title: '面积(平方公里)',
            'x-component': 'InputNumber',
            'x-component-props': {
              step: 0.01,
            },
          },
        },
        {
          name: 'description',
          type: 'text',
          interface: 'textarea',
          uiSchema: {
            type: 'string',
            title: '描述',
            'x-component': 'Input.TextArea',
          },
        },
        {
          name: 'status',
          type: 'string',
          defaultValue: 'active',
          interface: 'select',
          uiSchema: {
            type: 'string',
            title: '状态',
            'x-component': 'Select',
            enum: [
              { label: '启用', value: 'active' },
              { label: '禁用', value: 'inactive' },
            ],
          },
        },
        {
          name: 'createdAt',
          type: 'date',
          field: 'createdAt',
          interface: 'createdAt',
          uiSchema: {
            type: 'datetime',
            title: '创建时间',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
            },
            'x-read-pretty': true,
          },
        },
        {
          name: 'updatedAt',
          type: 'date',
          field: 'updatedAt',
          interface: 'updatedAt',
          uiSchema: {
            type: 'datetime',
            title: '更新时间',
            'x-component': 'DatePicker',
            'x-component-props': {
              showTime: true,
            },
            'x-read-pretty': true,
          },
        },
      ],
    };
    
    const result = await client.post('/collections', collectionData);
    console.log('镇街集合创建成功:', result.data);
    
  } catch (error) {
    console.error('创建镇街集合失败:', error.message);
    if (error.response?.data) {
      console.error('错误详情:', error.response.data);
    }
  }
}

async function main() {
  await queryTownsCollections();
  
  // 如果没有找到镇街集合，询问是否创建
  console.log('\n是否需要创建镇街集合？(y/n)');
  // 这里直接创建，实际使用时可以添加交互逻辑
  await createTownsCollection();
}

main().catch(console.error);

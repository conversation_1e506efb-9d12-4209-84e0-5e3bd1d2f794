#!/usr/bin/env node

// 深度分析 page_1 的完整结构
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let step = 0;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          step = 1;
          console.log('=== 分析 page_1 主页面 Schema ===');
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: 'omx0bqn3fxy' } } });
        } 
        else if (msg.id === 2 && step === 1) {
          console.log('page_1 主页面原始 Schema:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 2;
          console.log('\n=== 分析 page_1 主页面 Properties ===');
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: 'omx0bqn3fxy' } } });
        }
        else if (msg.id === 3 && step === 2) {
          console.log('page_1 主页面 Properties:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 3;
          console.log('\n=== 分析 page_1 tabs 子路由 Schema ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: 'c7owgwgeww4' } } });
        }
        else if (msg.id === 4 && step === 3) {
          console.log('page_1 tabs 子路由原始 Schema:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 4;
          console.log('\n=== 分析 page_1 tabs 子路由 Properties ===');
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: 'c7owgwgeww4' } } });
        }
        else if (msg.id === 5 && step === 4) {
          console.log('page_1 tabs 子路由 Properties:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          step = 5;
          console.log('\n=== 尝试列出 page_1 tabs 的区块 ===');
          send(server, { jsonrpc: '2.0', id: 6, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: 'c7owgwgeww4' } } });
        }
        else if (msg.id === 6 && step === 5) {
          console.log('page_1 tabs 区块列表:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 分析完成 ===');
          console.log('关键发现:');
          console.log('1. page_1 主页面: 简单的 Page 组件');
          console.log('2. page_1 tabs 子路由: Grid 组件 + x-initializer: "page:addBlock"');
          console.log('3. 即使有正确的结构，tabs 子路由也显示 0 个区块');
          console.log('4. 这说明问题可能不在结构上，而在其他地方');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'analyze-page1', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });

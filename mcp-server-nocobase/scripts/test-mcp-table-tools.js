#!/usr/bin/env node

/**
 * 测试 MCP 表格操作工具
 * 
 * 这个脚本模拟 AI 客户端通过 MCP 协议调用表格操作工具
 */

import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { NocoBaseClient } from '../dist/client.js';
import { registerTableOperationTools } from '../dist/tools/table-operations.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
  app: 'mcp_playground'
});

async function testMCPTableTools() {
  console.log('🚀 测试 MCP 表格操作工具...\n');

  // 创建 MCP 服务器实例
  const server = new McpServer({
    name: "test-table-operations",
    version: "0.1.0",
  });

  // 注册表格操作工具
  await registerTableOperationTools(server, client);

  // 验证工具注册
  console.log('📋 表格操作工具已注册到 MCP 服务器');
  console.log('预期工具列表：');
  const expectedTools = [
    'add_table_action',
    'remove_table_action',
    'update_table_action',
    'list_table_actions',
    'configure_table_column',
    'configure_table_filter',
    'configure_table_sort',
    'send_custom_request'
  ];
  expectedTools.forEach(tool => {
    console.log(`  - ${tool}`);
  });
  console.log();

  // 模拟 AI 调用工具的场景
  console.log('🤖 模拟 AI 调用 MCP 工具...\n');

  try {
    // 1. 测试添加表格操作按钮工具
    console.log('1️⃣ 测试 add_table_action 工具');
    const addActionResult = await server.callTool({
      name: 'add_table_action',
      arguments: {
        tableUid: 'test_table_uid_123',
        actionType: 'create'
      }
    });
    console.log('✅ add_table_action 调用成功：');
    console.log(JSON.stringify(addActionResult, null, 2));
    console.log();

    // 2. 测试自定义操作按钮
    console.log('2️⃣ 测试添加自定义操作按钮');
    const customActionResult = await server.callTool({
      name: 'add_table_action',
      arguments: {
        tableUid: 'test_table_uid_123',
        actionType: 'custom',
        customConfig: {
          title: '{{t("Send Email")}}',
          action: 'sendEmail',
          icon: 'MailOutlined',
          type: 'default',
          align: 'right',
          requiresACL: true,
          aclAction: 'sendEmail'
        },
        position: 'beforeEnd'
      }
    });
    console.log('✅ 自定义操作按钮添加成功：');
    console.log(JSON.stringify(customActionResult, null, 2));
    console.log();

    // 3. 测试配置表格列工具
    console.log('3️⃣ 测试 configure_table_column 工具');
    const columnResult = await server.callTool({
      name: 'configure_table_column',
      arguments: {
        tableUid: 'test_table_uid_123',
        fieldName: 'email',
        columnConfig: {
          title: '邮箱地址',
          width: 200,
          sortable: true,
          filterable: true,
          component: 'Input.Email'
        }
      }
    });
    console.log('✅ configure_table_column 调用成功：');
    console.log(JSON.stringify(columnResult, null, 2));
    console.log();

    // 4. 测试配置表格筛选器工具
    console.log('4️⃣ 测试 configure_table_filter 工具');
    const filterResult = await server.callTool({
      name: 'configure_table_filter',
      arguments: {
        tableUid: 'test_table_uid_123',
        filterConfig: {
          defaultFilter: {
            status: 'active'
          },
          enableQuickFilter: true,
          quickFilterFields: ['name', 'email']
        }
      }
    });
    console.log('✅ configure_table_filter 调用成功：');
    console.log(JSON.stringify(filterResult, null, 2));
    console.log();

    // 5. 测试配置表格排序工具
    console.log('5️⃣ 测试 configure_table_sort 工具');
    const sortResult = await server.callTool({
      name: 'configure_table_sort',
      arguments: {
        tableUid: 'test_table_uid_123',
        sortConfig: {
          defaultSort: ['-createdAt'],
          enableDragSort: false
        }
      }
    });
    console.log('✅ configure_table_sort 调用成功：');
    console.log(JSON.stringify(sortResult, null, 2));
    console.log();

    // 6. 测试列出表格操作工具
    console.log('6️⃣ 测试 list_table_actions 工具');
    const listResult = await server.callTool({
      name: 'list_table_actions',
      arguments: {
        tableUid: 'test_table_uid_123'
      }
    });
    console.log('✅ list_table_actions 调用成功：');
    console.log(JSON.stringify(listResult, null, 2));
    console.log();

    // 7. 测试发送自定义请求工具
    console.log('7️⃣ 测试 send_custom_request 工具');
    const requestResult = await server.callTool({
      name: 'send_custom_request',
      arguments: {
        requestId: 'export_users',
        requestData: {
          currentRecord: null,
          selectedRecords: [],
          formData: {
            format: 'xlsx'
          }
        }
      }
    });
    console.log('✅ send_custom_request 调用成功：');
    console.log(JSON.stringify(requestResult, null, 2));
    console.log();

    console.log('🎉 所有 MCP 表格操作工具测试完成！');
    
    // 总结
    console.log('\n📊 测试总结：');
    console.log('- ✅ add_table_action (添加表格操作按钮)');
    console.log('- ✅ configure_table_column (配置表格列)');
    console.log('- ✅ configure_table_filter (配置表格筛选器)');
    console.log('- ✅ configure_table_sort (配置表格排序)');
    console.log('- ✅ list_table_actions (列出表格操作)');
    console.log('- ✅ send_custom_request (发送自定义请求)');
    
    console.log('\n💡 使用说明：');
    console.log('这些工具现在可以被 AI 客户端通过 MCP 协议调用，例如：');
    console.log('- Claude Desktop');
    console.log('- 其他支持 MCP 的 AI 应用');
    console.log('- 自定义 MCP 客户端');

  } catch (error) {
    console.error('❌ 测试过程中出现错误：', error);
    if (error.message.includes('404') || error.message.includes('ENOTFOUND')) {
      console.log('💡 提示：这是正常的，因为我们使用的是测试 UID，实际使用时需要真实的表格 UID');
    }
  }
}

// AI 调用示例
function showAIUsageExamples() {
  console.log('\n🤖 AI 调用示例：\n');
  
  console.log('示例 1: AI 为表格添加新增按钮');
  console.log('AI 输入: "为用户表格添加一个新增按钮"');
  console.log('MCP 调用:');
  console.log(JSON.stringify({
    tool: 'add_table_action',
    arguments: {
      tableUid: 'users_table_uid',
      actionType: 'create'
    }
  }, null, 2));
  console.log();

  console.log('示例 2: AI 配置表格列');
  console.log('AI 输入: "为用户表格添加一个邮箱列，宽度200px，可排序"');
  console.log('MCP 调用:');
  console.log(JSON.stringify({
    tool: 'configure_table_column',
    arguments: {
      tableUid: 'users_table_uid',
      fieldName: 'email',
      columnConfig: {
        title: '邮箱地址',
        width: 200,
        sortable: true,
        component: 'Input.Email'
      }
    }
  }, null, 2));
  console.log();

  console.log('示例 3: AI 添加自定义操作');
  console.log('AI 输入: "为表格添加一个发送邮件的自定义操作按钮"');
  console.log('MCP 调用:');
  console.log(JSON.stringify({
    tool: 'add_table_action',
    arguments: {
      tableUid: 'users_table_uid',
      actionType: 'custom',
      customConfig: {
        title: '{{t("Send Email")}}',
        action: 'sendEmail',
        icon: 'MailOutlined',
        requiresACL: true,
        aclAction: 'sendEmail'
      }
    }
  }, null, 2));
}

// 运行测试
testMCPTableTools()
  .then(() => {
    showAIUsageExamples();
  })
  .catch(console.error);

#!/usr/bin/env node

// 对比 page_1 和 MCP 创建的测试页面，找出差异
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let page1Route = null;
  let mcpRoute = null;

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // 获取所有路由
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          let raw = ''; for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw); const list = payload?.data || payload || [];
          const pages = []; (function walk(nodes=[]) { for (const n of nodes) { if (n.type === 'page') pages.push(n); if (Array.isArray(n.children)) walk(n.children); } })(list);
          
          // 找到 page_1 和 MCP 测试页面
          page1Route = pages.find(p => p.title === 'page_1');
          mcpRoute = pages.find(p => typeof p.title === 'string' && p.title.startsWith('MCP 测试页面'));
          
          if (!page1Route) { console.log('未找到 page_1'); server.kill(); return; }
          if (!mcpRoute) { console.log('未找到 MCP 测试页面'); server.kill(); return; }
          
          console.log('=== page_1 路由信息 ===');
          console.log(JSON.stringify(page1Route, null, 2));
          console.log('\n=== MCP 测试页面路由信息 ===');
          console.log(JSON.stringify(mcpRoute, null, 2));
          
          // 获取 page_1 的详细信息
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'get_route', arguments: { id: page1Route.id } } });
        } else if (msg.id === 3) {
          console.log('\n=== page_1 详细路由信息 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 获取 MCP 页面的详细信息
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_route', arguments: { id: mcpRoute.id } } });
        } else if (msg.id === 4) {
          console.log('\n=== MCP 测试页面详细路由信息 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 分析 page_1 的 UI Schema
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'analyze_page_schema', arguments: { schemaUid: page1Route.schemaUid } } });
        } else if (msg.id === 5) {
          console.log('\n=== page_1 UI Schema 分析 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 分析 MCP 页面的 UI Schema
          send(server, { jsonrpc: '2.0', id: 6, method: 'tools/call', params: { name: 'analyze_page_schema', arguments: { schemaUid: mcpRoute.schemaUid } } });
        } else if (msg.id === 6) {
          console.log('\n=== MCP 测试页面 UI Schema 分析 ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 获取 page_1 的原始 schema
          send(server, { jsonrpc: '2.0', id: 7, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: page1Route.schemaUid } } });
        } else if (msg.id === 7) {
          console.log('\n=== page_1 原始 UI Schema ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          // 获取 MCP 页面的原始 schema
          send(server, { jsonrpc: '2.0', id: 8, method: 'tools/call', params: { name: 'get_page_schema', arguments: { schemaUid: mcpRoute.schemaUid } } });
        } else if (msg.id === 8) {
          console.log('\n=== MCP 测试页面原始 UI Schema ===');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'compare-pages', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });

#!/usr/bin/env node

/**
 * 测试 NocoBase Roles API 功能
 * 
 * 使用方法：
 * node scripts/test-roles.js
 */

import { Client } from '@modelcontextprotocol/sdk/client/index.js';
import { StdioClientTransport } from '@modelcontextprotocol/sdk/client/stdio.js';

async function testRolesAPI() {
  console.log('🚀 Starting NocoBase Roles API Test...\n');

  // 创建 MCP 客户端
  const transport = new StdioClientTransport({
    command: 'node',
    args: [
      'dist/index.js',
      '--base-url', 'https://app.dev.orb.local/api',
      '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
      '--app', 'mcp_playground'
    ]
  });

  const mcpClient = new Client({
    name: 'test-roles-client',
    version: '1.0.0'
  });

  let testResults = {
    passed: 0,
    failed: 0,
    errors: []
  };

  function logTest(name, success, error = null) {
    if (success) {
      console.log(`✅ ${name}`);
      testResults.passed++;
    } else {
      console.log(`❌ ${name}: ${error}`);
      testResults.failed++;
      testResults.errors.push({ test: name, error });
    }
  }

  try {
    await mcpClient.connect(transport);
    
    // 等待服务器启动
    console.log('⏳ Waiting for server to start...');
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('\n=== NocoBase Roles API 测试 ===\n');

    // 2. 列出工具
    console.log('📋 Step 1: List tools');
    try {
      const tools = await mcpClient.listTools();
      const roleTools = tools.tools?.filter(t => t.name.includes('role')) || [];
      console.log('Role-related tools:', roleTools.map(t => t.name).join(', '));
      logTest('List role tools', roleTools.length > 0, roleTools.length === 0 ? 'No role tools found' : null);
    } catch (error) {
      logTest('List role tools', false, error.message);
    }

    // 3. 列出角色
    console.log('\n📋 Step 2: List roles');
    try {
      const result = await mcpClient.callTool({
        name: 'list_roles',
        arguments: {
          page: 1,
          pageSize: 10
        }
      });
      console.log('List roles result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('List roles', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('List roles', false, error.message);
    }

    // 4. 获取特定角色（假设角色名为admin）
    console.log('\n📋 Step 3: Get role by name');
    try {
      const result = await mcpClient.callTool({
        name: 'get_role',
        arguments: {
          name: 'admin'
        }
      });
      console.log('Get role result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Get role by name', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Get role by name', false, error.message);
    }

    // 5. 检查当前角色
    console.log('\n📋 Step 4: Check current role');
    try {
      const result = await mcpClient.callTool({
        name: 'check_role',
        arguments: {}
      });
      console.log('Check role result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Check current role', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Check current role', false, error.message);
    }

    // 6. 创建测试角色
    console.log('\n📋 Step 5: Create test role');
    try {
      const result = await mcpClient.callTool({
        name: 'create_role',
        arguments: {
          title: 'Test Role',
          description: 'A test role created by MCP',
          strategy: {
            actions: ['view', 'create']
          },
          hidden: false,
          allowConfigure: false
        }
      });
      console.log('Create role result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Create test role', !result.isError, result.isError ? result.content?.[0]?.text : null);
      
      // 如果创建成功，尝试解析角色名用于后续测试
      if (!result.isError) {
        try {
          const roleText = result.content?.[0]?.text || '';
          const roleMatch = roleText.match(/"name":\s*"([^"]+)"/);
          if (roleMatch) {
            const newRoleName = roleMatch[1];
            console.log(`Created role with name: ${newRoleName}`);
            
            // 7. 更新角色
            console.log('\n📋 Step 6: Update role');
            try {
              const updateResult = await mcpClient.callTool({
                name: 'update_role',
                arguments: {
                  name: newRoleName,
                  title: 'Updated Test Role',
                  description: 'Updated description'
                }
              });
              console.log('Update role result:', updateResult.content?.[0]?.text?.substring(0, 200) + '...');
              logTest('Update role', !updateResult.isError, updateResult.isError ? updateResult.content?.[0]?.text : null);
            } catch (error) {
              logTest('Update role', false, error.message);
            }

            // 8. 删除测试角色
            console.log('\n📋 Step 7: Delete test role');
            try {
              const deleteResult = await mcpClient.callTool({
                name: 'delete_role',
                arguments: {
                  name: newRoleName
                }
              });
              console.log('Delete role result:', deleteResult.content?.[0]?.text);
              logTest('Delete test role', !deleteResult.isError, deleteResult.isError ? deleteResult.content?.[0]?.text : null);
            } catch (error) {
              logTest('Delete test role', false, error.message);
            }
          }
        } catch (parseError) {
          console.log('Could not parse created role name for further testing');
        }
      }
    } catch (error) {
      logTest('Create test role', false, error.message);
    }

    // 9. 测试过滤功能
    console.log('\n📋 Step 8: Test role filtering');
    try {
      const result = await mcpClient.callTool({
        name: 'list_roles',
        arguments: {
          filter: { "hidden": false },
          appends: ["users"]
        }
      });
      console.log('Filter roles result:', result.content?.[0]?.text?.substring(0, 200) + '...');
      logTest('Filter roles', !result.isError, result.isError ? result.content?.[0]?.text : null);
    } catch (error) {
      logTest('Filter roles', false, error.message);
    }

  } catch (error) {
    console.error('❌ Test failed with error:', error);
    testResults.failed++;
    testResults.errors.push({ test: 'Overall test', error: error.message });
  } finally {
    // 清理
    try {
      await mcpClient.close();
    } catch (e) {
      // 忽略关闭错误
    }
    
    // 输出测试结果
    console.log('\n' + '='.repeat(50));
    console.log('📊 Test Results:');
    console.log(`✅ Passed: ${testResults.passed}`);
    console.log(`❌ Failed: ${testResults.failed}`);
    
    if (testResults.errors.length > 0) {
      console.log('\n🔍 Error Details:');
      testResults.errors.forEach(({ test, error }) => {
        console.log(`  - ${test}: ${error}`);
      });
    }
    
    console.log('\n🎯 Roles API tools are now available for NocoBase MCP server!');
    process.exit(testResults.failed > 0 ? 1 : 0);
  }
}

// 运行测试
testRolesAPI().catch(console.error);

#!/usr/bin/env node

// 测试 Markdown 区块创建的详细过程
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        
        if (msg.id === 1 && msg.result) {
          console.log('=== 测试向我创建的页面添加 Markdown 区块 ===');
          // 使用我刚创建的页面的 tabs 子路由
          send(server, { 
            jsonrpc: '2.0', 
            id: 2, 
            method: 'tools/call', 
            params: { 
              name: 'add_markdown_block', 
              arguments: { 
                parentUid: 'tabs-1754637680244-cuu5747th', // 我创建的页面的 tabs schemaUid
                title: '测试区块 - 新页面',
                content: '# 成功！\n\n这个区块添加到了我创建的页面中。'
              } 
            } 
          });
        } 
        else if (msg.id === 2) {
          console.log('添加区块到新页面的结果:');
          const content = msg.result?.content || msg.error?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 验证新页面的区块 ===');
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'list_page_blocks', arguments: { schemaUid: 'tabs-1754637680244-cuu5747th' } } });
        }
        else if (msg.id === 3) {
          console.log('新页面的区块列表:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 检查新页面的 Properties ===');
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'get_schema_properties', arguments: { schemaUid: 'tabs-1754637680244-cuu5747th' } } });
        }
        else if (msg.id === 4) {
          console.log('新页面的 Properties:');
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          
          console.log('\n=== 总结 ===');
          console.log('如果区块添加成功，请检查页面 "page_1 副本 1754637679986" 是否显示了区块');
          console.log('如果还是失败，说明问题在 insertBlockToGrid 方法中');
          
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'test-markdown', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });

#!/usr/bin/env node

import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [ 'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // dump all routes
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          // find latest page titled with prefix 'MCP 测试页面'
          let raw = '';
          for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          const payload = JSON.parse(raw);
          const list = payload?.data || payload || [];
          const pages = [];
          function walk(nodes=[]) { for (const n of nodes) { if (n.type === 'page') pages.push(n); if (Array.isArray(n.children)) walk(n.children); } }
          walk(list);
          const candidates = pages.filter(p => typeof p.title === 'string' && p.title.startsWith('MCP 测试页面'));
          if (candidates.length === 0) { console.log('未找到以 MCP 测试页面 开头的页面'); server.kill(); return; }
          candidates.sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
          const target = candidates[0];
          console.log('将为该页面开启标题栏与显示标题:', target.title, target.id);
          // update route: enableHeader = true, displayTitle = true
          send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'update_route', arguments: { id: target.id, hidden: false, } } });
          // note: our update_route supports title, icon, hidden, href, openInNewWindow. It doesn't expose enableHeader/displayTitle.
          // fallback: we can directly call admin API via raw patch if needed, but keeping within MCP tools.
          // After toggling hidden to false (already false), we will at least verify list again.
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'list_routes', arguments: { tree: true } } });
        } else if (msg.id === 4) {
          let raw = '';
          for (const item of (msg.result?.content || [])) if (item?.type === 'text') raw += item.text;
          console.log(raw);
          server.kill();
        }
      } catch {}
    }
  });

  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'enable-header', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });


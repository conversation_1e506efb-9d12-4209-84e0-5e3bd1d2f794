#!/usr/bin/env node

// 在“page_1”菜单后面创建一个测试页面路由
import { spawn } from 'child_process';

const config = {
  baseUrl: 'http://**************:13000/api',
  token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
  app: 'mcp_playground'
};

function send(server, payload) { server.stdin.write(JSON.stringify(payload) + '\n'); }

async function main() {
  const server = spawn('node', [
    'dist/index.js', '--base-url', config.baseUrl, '--token', config.token, '--app', config.app
  ], { stdio: ['pipe', 'pipe', 'pipe'] });

  server.stderr.on('data', (d) => process.stderr.write('stderr: ' + d.toString()));

  let routeMap = new Map();

  server.stdout.on('data', (data) => {
    const lines = data.toString().split('\n').filter(Boolean);
    for (const line of lines) {
      try {
        const msg = JSON.parse(line);
        if (msg.id === 1 && msg.result) {
          // 先列出路由（全部）
          send(server, { jsonrpc: '2.0', id: 2, method: 'tools/call', params: { name: 'routes_dump_raw_direct_all', arguments: {} } });
        } else if (msg.id === 2) {
          const content = msg.result?.content || [];
          let raw = '';
          for (const item of content) if (item?.type === 'text') raw += item.text;
          try {
            const json = JSON.parse(raw);
            const list = json?.data || json || [];
            // 深度遍历，找到 title === 'page_1'
            function walk(nodes = []) {
              for (const n of nodes) {
                if (n.title === 'page_1') return n;
                if (Array.isArray(n.children)) {
                  const found = walk(n.children);
                  if (found) return found;
                }
              }
              return null;
            }
            const target = walk(list);
            if (!target || !target.id) {
              console.log('未找到名为 page_1 的菜单，无法定位插入位置');
              server.kill();
              return;
            }
            const pageTitle = `MCP 测试页面 ${Date.now()}`;
            // 创建页面路由
            send(server, { jsonrpc: '2.0', id: 3, method: 'tools/call', params: { name: 'create_page_route', arguments: { title: pageTitle, template: 'dashboard', icon: 'ExperimentOutlined' } } });
            // 保存目标 id
            routeMap.set('targetId', target.id);
          } catch (e) {
            console.log('解析 routes_dump_raw_direct_all 响应失败');
            server.kill();
          }
        } else if (msg.id === 3) {
          // 拿到创建结果中的新路由 id
          const content = msg.result?.content || [];
          let raw = '';
          for (const item of content) if (item?.type === 'text') raw += item.text;
          const match = raw.match(/\{[\s\S]*\}/);
          if (!match) { console.log('未能解析新建路由返回'); server.kill(); return; }
          const created = JSON.parse(match[0]);
          if (!created?.id) { console.log('新建路由未返回 id'); server.kill(); return; }
          const sourceId = created.id;
          const targetId = routeMap.get('targetId');
          // 移动路由到 page_1 后面（insertAfter）
          send(server, { jsonrpc: '2.0', id: 4, method: 'tools/call', params: { name: 'move_route', arguments: { sourceId, targetId, method: 'insertAfter' } } });
        } else if (msg.id === 4) {
          console.log('已将新路由移动到 page_1 后面');
          // 列出树形路由确认
          send(server, { jsonrpc: '2.0', id: 5, method: 'tools/call', params: { name: 'list_routes', arguments: { tree: true } } });
        } else if (msg.id === 5) {
          const content = msg.result?.content || [];
          for (const item of content) if (item?.type === 'text') console.log(item.text);
          server.kill();
        }
      } catch {}
    }
  });

  // 初始化 MCP
  send(server, { jsonrpc: '2.0', id: 1, method: 'initialize', params: { protocolVersion: '2024-11-05', capabilities: { tools: {} }, clientInfo: { name: 'create-after-page1', version: '1.0.0' } } });
}

main().catch((e) => { console.error(e); process.exit(1); });


#!/usr/bin/env node

// 修复镇街集合，添加缺失的默认字段定义
import { NocoBaseClient } from './dist/client.js';

async function fixTownsCollection() {
  console.log('🔧 修复镇街集合，添加缺失的默认字段定义...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 1. 添加ID字段定义
    console.log('📋 添加ID字段定义');
    try {
      const idField = await client.createField('towns', {
        name: 'id',
        type: 'bigInt',
        interface: 'id',
        autoIncrement: true,
        primaryKey: true,
        allowNull: false,
        uiSchema: {
          type: 'number',
          title: '{{t("ID")}}',
          'x-component': 'InputNumber',
          'x-read-pretty': true
        }
      });
      console.log(`✅ 成功创建ID字段定义: ${idField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  ID字段定义已存在，跳过创建');
      } else {
        console.log(`❌ 创建ID字段定义失败: ${error.message}`);
      }
    }

    // 2. 添加创建时间字段定义
    console.log('📋 添加创建时间字段定义');
    try {
      const createdAtField = await client.createField('towns', {
        name: 'createdAt',
        type: 'date',
        interface: 'createdAt',
        field: 'createdAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Created at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
      console.log(`✅ 成功创建创建时间字段定义: ${createdAtField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  创建时间字段定义已存在，跳过创建');
      } else {
        console.log(`❌ 创建创建时间字段定义失败: ${error.message}`);
      }
    }

    // 3. 添加更新时间字段定义
    console.log('📋 添加更新时间字段定义');
    try {
      const updatedAtField = await client.createField('towns', {
        name: 'updatedAt',
        type: 'date',
        interface: 'updatedAt',
        field: 'updatedAt',
        uiSchema: {
          type: 'datetime',
          title: '{{t("Last updated at")}}',
          'x-component': 'DatePicker',
          'x-component-props': {},
          'x-read-pretty': true
        }
      });
      console.log(`✅ 成功创建更新时间字段定义: ${updatedAtField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  更新时间字段定义已存在，跳过创建');
      } else {
        console.log(`❌ 创建更新时间字段定义失败: ${error.message}`);
      }
    }

    // 4. 添加创建人字段定义
    console.log('📋 添加创建人字段定义');
    try {
      const createdByField = await client.createField('towns', {
        name: 'createdBy',
        type: 'belongsTo',
        interface: 'createdBy',
        target: 'users',
        foreignKey: 'createdById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Created by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
      console.log(`✅ 成功创建创建人字段定义: ${createdByField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  创建人字段定义已存在，跳过创建');
      } else {
        console.log(`❌ 创建创建人字段定义失败: ${error.message}`);
      }
    }

    // 5. 添加更新人字段定义
    console.log('📋 添加更新人字段定义');
    try {
      const updatedByField = await client.createField('towns', {
        name: 'updatedBy',
        type: 'belongsTo',
        interface: 'updatedBy',
        target: 'users',
        foreignKey: 'updatedById',
        targetKey: 'id',
        uiSchema: {
          type: 'object',
          title: '{{t("Last updated by")}}',
          'x-component': 'AssociationField',
          'x-component-props': {
            fieldNames: {
              value: 'id',
              label: 'nickname'
            }
          },
          'x-read-pretty': true
        }
      });
      console.log(`✅ 成功创建更新人字段定义: ${updatedByField.name}`);
    } catch (error) {
      if (error.message.includes('already exists') || error.message.includes('duplicate')) {
        console.log('ℹ️  更新人字段定义已存在，跳过创建');
      } else {
        console.log(`❌ 创建更新人字段定义失败: ${error.message}`);
      }
    }

    console.log();
    console.log('🎉 镇街集合字段修复完成！');

    // 验证修复结果
    console.log('\n🔍 验证修复结果:');
    const fields = await client.listFields('towns');
    console.log(`✅ 现在共有 ${fields.length} 个字段:`);
    fields.forEach(field => {
      console.log(`   • ${field.name} (${field.type}) - ${field.interface || 'no interface'} - ${field.uiSchema?.title || '无标题'}`);
    });

  } catch (error) {
    console.error('❌ 修复过程中发生错误:', error.message);
  }
}

fixTownsCollection().catch(console.error);

#!/usr/bin/env node

const axios = require('axios');

// NocoBase API 配置
const config = {
  baseURL: 'http://103.121.94.113:13000/api',
  headers: {
    'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY',
    'X-App': 'mcp_playground',
    'Content-Type': 'application/json'
  }
};

// 生成唯一ID的辅助函数
function generateUID(prefix = 'uid') {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// API 调用辅助函数
async function apiCall(method, url, data = null) {
  try {
    const response = await axios({
      method,
      url: `${config.baseURL}${url}`,
      headers: config.headers,
      data
    });
    return response.data;
  } catch (error) {
    console.error(`API 调用失败 ${method} ${url}:`, error.response?.data || error.message);
    throw error;
  }
}

// 创建分组路由
async function createGroupRoute(title, parentId = null, icon = 'FolderOutlined') {
  console.log(`创建分组: ${title}${parentId ? ` (父级ID: ${parentId})` : ''}`);
  
  const schemaUid = generateUID('group');
  
  // 创建分组的 UI Schema
  const groupSchema = {
    type: "void",
    "x-component": "Menu.SubMenu",
    "x-decorator": "ACLMenuItemProvider",
    "x-component-props": {
      icon,
      popupClassName: "nb-menu-popup"
    },
    "x-server-hooks": [
      {
        type: "onSelfCreate",
        method: "bindMenuToRole"
      }
    ],
    title,
    "x-uid": schemaUid,
    "x-async": false
  };

  await apiCall('POST', '/uiSchemas:insert', groupSchema);

  // 创建路由记录
  const routeData = {
    type: "group",
    title,
    schemaUid,
    parentId,
    icon,
    hidden: false,
    sort: parentId ? 1 : 10
  };

  const result = await apiCall('POST', '/desktopRoutes:create', routeData);
  console.log(`✓ 分组 ${title} 创建成功，ID: ${result.data.id}`);
  
  return result.data;
}

// 创建页面路由
async function createPageRoute(title, parentId = null, icon = 'FileOutlined') {
  console.log(`创建页面: ${title}${parentId ? ` (父级ID: ${parentId})` : ''}`);
  
  const pageSchemaUid = generateUID('page');
  const menuSchemaUid = generateUID('menu');
  const tabSchemaUid = generateUID('tab');
  const tabSchemaName = generateUID('tab');

  // 1. 创建页面 Schema
  const pageSchema = {
    type: "void",
    "x-component": "Page",
    "x-async": true,
    "x-index": 1,
    properties: {
      [tabSchemaName]: {
        type: "void",
        "x-component": "Grid",
        "x-initializer": "page:addBlock",
        "x-uid": tabSchemaUid,
        "x-async": true,
        "x-index": 1
      }
    },
    "x-uid": pageSchemaUid
  };

  await apiCall('POST', '/uiSchemas:insert', pageSchema);

  // 2. 创建路由记录
  const routeData = {
    type: "page",
    title,
    schemaUid: pageSchemaUid,
    menuSchemaUid,
    tabSchemaName,
    parentId,
    icon,
    enableTabs: false,
    hidden: false,
    sort: parentId ? 1 : 10
  };

  const routeResult = await apiCall('POST', '/desktopRoutes:create', routeData);

  // 3. 创建菜单项 Schema
  const menuItemSchema = {
    type: "void",
    title,
    'x-component': "Menu.Item",
    'x-designer': "Menu.Item.Designer",
    'x-component-props': {
      icon,
      __route__: routeResult.data
    },
    'x-uid': menuSchemaUid,
    'x-async': false
  };

  // 确定父菜单UID
  let parentMenuUid = "nocobase-admin-menu";
  if (parentId) {
    try {
      const parentRoute = await apiCall('GET', `/desktopRoutes/${parentId}`);
      if (parentRoute.data && parentRoute.data.schemaUid) {
        parentMenuUid = parentRoute.data.schemaUid;
      }
    } catch (error) {
      console.warn(`无法获取父路由 ${parentId} 的信息，使用默认菜单`);
    }
  }

  await apiCall('POST', `/uiSchemas:insertAdjacent/${parentMenuUid}?position=beforeEnd`, {
    schema: menuItemSchema
  });

  // 4. 创建 tabs 子路由
  const tabsRouteData = {
    type: "tabs",
    parentId: routeResult.data.id,
    schemaUid: tabSchemaUid,
    tabSchemaName,
    hidden: true,
    sort: 1
  };

  await apiCall('POST', '/desktopRoutes:create', tabsRouteData);

  console.log(`✓ 页面 ${title} 创建成功，ID: ${routeResult.data.id}`);
  return routeResult.data;
}

async function createGroup3Structure() {
  console.log('🚀 开始创建 group3 及其嵌套页面结构\n');

  try {
    // 1. 创建 group_3
    console.log('1. 创建 group_3...');
    const group3 = await createGroupRoute('group_3', null, 'AppstoreOutlined');

    // 2. 在 group_3 下创建 group3_page1
    console.log('\n2. 在 group_3 下创建 group3_page1...');
    await createPageRoute('group3_page1', group3.id, 'FileTextOutlined');

    // 3. 在 group_3 下创建 group3_subgroup
    console.log('\n3. 在 group_3 下创建 group3_subgroup...');
    const group3Subgroup = await createGroupRoute('group3_subgroup', group3.id, 'FolderOpenOutlined');

    // 4. 在 group3_subgroup 下创建 subgroup_page1
    console.log('\n4. 在 group3_subgroup 下创建 subgroup_page1...');
    await createPageRoute('subgroup_page1', group3Subgroup.id, 'FileOutlined');

    // 5. 在 group3_subgroup 下创建 subgroup_page2
    console.log('\n5. 在 group3_subgroup 下创建 subgroup_page2...');
    await createPageRoute('subgroup_page2', group3Subgroup.id, 'FileDoneOutlined');

    // 6. 在 group_3 下再创建一个页面 group3_page2
    console.log('\n6. 在 group_3 下创建 group3_page2...');
    await createPageRoute('group3_page2', group3.id, 'FileImageOutlined');

    console.log('\n🎉 group3 结构创建完成！');
    console.log('\n创建的结构：');
    console.log('group_3/');
    console.log('├── group3_page1');
    console.log('├── group3_subgroup/');
    console.log('│   ├── subgroup_page1');
    console.log('│   └── subgroup_page2');
    console.log('└── group3_page2');

  } catch (error) {
    console.error('❌ 创建过程中出现错误:', error.message);
  }
}

// 运行创建脚本
createGroup3Structure();

#!/usr/bin/env node

// 创建标准的集合分类
import { NocoBaseClient } from './dist/client.js';

async function createStandardCategories() {
  console.log('📂 创建标准集合分类...\n');

  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  // 定义标准分类
  const standardCategories = [
    {
      name: 'Administrative',
      color: 'blue',
      sort: 1,
      description: '行政管理相关的集合，如省市区县等行政区划'
    },
    {
      name: 'Geography',
      color: 'green',
      sort: 2,
      description: '地理位置相关的集合，如地址、坐标等'
    },
    {
      name: 'User Management',
      color: 'purple',
      sort: 3,
      description: '用户管理相关的集合，如用户、角色、权限等'
    },
    {
      name: 'Content Management',
      color: 'orange',
      sort: 4,
      description: '内容管理相关的集合，如文章、页面、媒体等'
    },
    {
      name: 'Commerce',
      color: 'red',
      sort: 5,
      description: '商务相关的集合，如产品、订单、客户等'
    },
    {
      name: 'System',
      color: 'gray',
      sort: 6,
      description: '系统相关的集合，如日志、设置、配置等'
    },
    {
      name: 'Workflow',
      color: 'yellow',
      sort: 7,
      description: '工作流相关的集合，如流程、任务、审批等'
    },
    {
      name: 'Analytics',
      color: 'cyan',
      sort: 8,
      description: '分析统计相关的集合，如报表、指标等'
    },
    {
      name: 'Communication',
      color: 'pink',
      sort: 9,
      description: '通讯相关的集合，如消息、通知、邮件等'
    },
    {
      name: 'Organization',
      color: 'indigo',
      sort: 10,
      description: '组织架构相关的集合，如部门、职位等'
    }
  ];

  try {
    // 检查现有分类
    console.log('🔍 检查现有分类...');
    const existingCategories = await client.listCollectionCategories();
    const existingNames = existingCategories.data.map(cat => cat.name);
    console.log(`现有分类: ${existingNames.join(', ') || '无'}\n`);

    // 创建标准分类
    const createdCategories = [];
    const skippedCategories = [];

    for (const category of standardCategories) {
      try {
        if (existingNames.includes(category.name)) {
          console.log(`⏭️  跳过已存在的分类: ${category.name}`);
          skippedCategories.push(category.name);
          continue;
        }

        console.log(`📂 创建分类: ${category.name}`);
        const createdCategory = await client.createCollectionCategory({
          name: category.name,
          color: category.color,
          sort: category.sort
        });
        
        console.log(`✅ 成功创建分类: ${createdCategory.name} (ID: ${createdCategory.id})`);
        createdCategories.push(createdCategory);
        
        // 添加小延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 100));
        
      } catch (error) {
        console.log(`❌ 创建分类 ${category.name} 失败: ${error.message}`);
      }
    }

    console.log('\n📊 创建结果统计:');
    console.log(`✅ 成功创建: ${createdCategories.length} 个分类`);
    console.log(`⏭️  跳过已存在: ${skippedCategories.length} 个分类`);
    console.log(`❌ 创建失败: ${standardCategories.length - createdCategories.length - skippedCategories.length} 个分类`);

    if (createdCategories.length > 0) {
      console.log('\n🎉 新创建的分类:');
      createdCategories.forEach(cat => {
        console.log(`   • ${cat.name} (${cat.color}) - Sort: ${cat.sort}`);
      });
    }

    if (skippedCategories.length > 0) {
      console.log('\n⏭️  跳过的分类:');
      skippedCategories.forEach(name => {
        console.log(`   • ${name}`);
      });
    }

    // 验证最终结果
    console.log('\n🔍 验证最终分类列表:');
    const finalCategories = await client.listCollectionCategories({ 
      pageSize: 50,
      sort: ['sort'] 
    });
    
    console.log(`总共 ${finalCategories.data.length} 个分类:`);
    finalCategories.data.forEach((cat, index) => {
      console.log(`   ${index + 1}. ${cat.name} (${cat.color || 'default'}) - Sort: ${cat.sort || 0}`);
    });

    console.log('\n💡 分类使用建议:');
    console.log('   • Administrative + Geography: 省市区县镇村等行政区划');
    console.log('   • User Management: 用户、角色、权限相关');
    console.log('   • Content Management: 文章、页面、媒体内容');
    console.log('   • Commerce: 产品、订单、客户、支付');
    console.log('   • System: 系统配置、日志、设置');
    console.log('   • Organization: 部门、职位、员工');

    console.log('\n🎯 下一步操作:');
    console.log('   1. 使用 create_collection 工具时指定 category 参数');
    console.log('   2. 为现有集合分配合适的分类');
    console.log('   3. 在NocoBase管理界面中查看分类效果');

  } catch (error) {
    console.error('❌ 创建标准分类过程中发生错误:', error.message);
  }
}

createStandardCategories().catch(console.error);

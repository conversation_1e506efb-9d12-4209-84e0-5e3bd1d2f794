import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient } from "../client.js";
import { uid } from "../utils.js";

/**
 * 表单字段类型定义
 */
export const FORM_FIELD_TYPES = {
  input: { component: 'Input', interface: 'input', type: 'string' },
  textarea: { component: 'Input.TextArea', interface: 'textarea', type: 'text' },
  number: { component: 'InputNumber', interface: 'number', type: 'integer' },
  select: { component: 'Select', interface: 'select', type: 'string' },
  radio: { component: 'Radio.Group', interface: 'radioGroup', type: 'string' },
  checkbox: { component: 'Checkbox.Group', interface: 'checkboxGroup', type: 'array' },
  date: { component: 'DatePicker', interface: 'date', type: 'date' },
  datetime: { component: 'DatePicker', interface: 'datetime', type: 'date' },
  association: { component: 'AssociationField', interface: 'linkTo', type: 'belongsTo' },
  upload: { component: 'Upload.Attachment', interface: 'attachment', type: 'belongsToMany' }
};

/**
 * 表单操作类型定义
 */
export const FORM_ACTION_TYPES = {
  submit: {
    title: "{{t('Submit')}}",
    action: 'submit',
    component: 'Action',
    type: 'primary',
    htmlType: 'submit',
    useComponentProps: 'useCreateActionProps'
  },
  update: {
    title: "{{t('Update')}}",
    action: 'submit',
    component: 'Action',
    type: 'primary',
    htmlType: 'submit',
    useComponentProps: 'useUpdateActionProps'
  },
  cancel: {
    title: "{{t('Cancel')}}",
    component: 'Action',
    useComponentProps: 'useCancelActionProps'
  }
};

/**
 * 创建表单字段 Schema
 */
export function createFormFieldSchema(options: {
  fieldName: string;
  fieldType: string;
  title?: string | undefined;
  required?: boolean;
}) {
  const { fieldName, fieldType, title, required = false } = options;
  const fieldConfig = FORM_FIELD_TYPES[fieldType as keyof typeof FORM_FIELD_TYPES];
  
  if (!fieldConfig) {
    throw new Error(`Unsupported field type: ${fieldType}`);
  }

  return {
    type: fieldConfig.type,
    name: fieldName,
    'x-uid': uid(),
    'x-decorator': 'FormItem',
    'x-component': fieldConfig.component,
    title: title || fieldName,
    required
  };
}

/**
 * 注册表单操作工具
 */
export async function registerFormOperationTools(server: McpServer, client: NocoBaseClient) {
  // 添加表单字段
  server.registerTool(
    'add_form_field',
    {
      title: 'Add Form Field',
      description: 'Add a field to a form block',
      inputSchema: {
        formUid: z.string().describe('The UID of the form block'),
        fieldName: z.string().describe('Name of the field'),
        fieldType: z.enum(['input', 'textarea', 'number', 'select', 'radio', 'checkbox', 'date', 'datetime', 'association', 'upload']).describe('Type of the field'),
        title: z.string().optional().describe('Display title of the field'),
        required: z.boolean().optional().default(false).describe('Whether the field is required')
      }
    },
    async (args) => {
      try {
        const { formUid, fieldName, fieldType, title, required = false } = args;
        
        const fieldSchema = createFormFieldSchema({
          fieldName,
          fieldType,
          title,
          required
        });

        return {
          content: [{
            type: "text",
            text: `Form field schema created: ${JSON.stringify({
              fieldUid: fieldSchema['x-uid'],
              fieldName,
              fieldType,
              title: title || fieldName
            }, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // 列出表单字段类型
  server.registerTool(
    'list_form_field_types',
    {
      title: 'List Form Field Types',
      description: 'List all supported form field types',
      inputSchema: {}
    },
    async () => {
      try {
        const fieldTypes = Object.entries(FORM_FIELD_TYPES).map(([type, config]) => ({
          type,
          component: config.component,
          interface: config.interface,
          dataType: config.type
        }));

        return {
          content: [{
            type: "text",
            text: `Supported form field types:\n${JSON.stringify(fieldTypes, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}

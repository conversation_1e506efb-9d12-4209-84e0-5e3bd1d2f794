/**
 * NocoBase Block Management Tools
 * 提供区块的创建、管理和操作功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';
import { BLOCK_TEMPLATES } from '../block-templates.js';
import { uid } from '../utils.js';

/**
 * 获取页面Schema工具
 */
export const getPageSchemaTool: Tool = {
  name: 'get_page_schema',
  description: 'Get the complete schema structure of a page',
  inputSchema: {
    type: 'object',
    properties: {
      schemaUid: {
        type: 'string',
        description: 'The UID of the page schema to retrieve'
      }
    },
    required: ['schemaUid']
  }
};

export async function handleGetPageSchema(client: NocoBaseClient, args: any) {
  try {
    const { schemaUid } = args;
    const schema = await client.getPageSchema(schemaUid);
    
    return {
      content: [
        {
          type: 'text',
          text: `Page schema retrieved successfully:\n${JSON.stringify(schema, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error retrieving page schema: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 列出页面区块工具
 */
export const listPageBlocksTool: Tool = {
  name: 'list_page_blocks',
  description: 'List all blocks in a page',
  inputSchema: {
    type: 'object',
    properties: {
      schemaUid: {
        type: 'string',
        description: 'The UID of the page schema'
      }
    },
    required: ['schemaUid']
  }
};

export async function handleListPageBlocks(client: NocoBaseClient, args: any) {
  try {
    const { schemaUid } = args;
    // 使用 getSchemaProperties 能拿到实际的属性树（包含动态插入的区块）
    const schema = await client.getSchemaProperties(schemaUid);

    // 递归查找所有区块
    const blocks: any[] = [];
    
    function findBlocks(obj: any, path: string = '') {
      if (obj && typeof obj === 'object') {
        // 识别区块容器：CardItem（无论是否有 x-decorator）
        if (obj['x-component'] === 'CardItem') {
          const inner = obj.properties ? Object.values(obj.properties)[0] as any : null;
          const blockType = inner?.['x-component'] === 'Markdown.Void'
            ? 'markdown'
            : (obj['x-decorator'] || '').toString();
          blocks.push({
            uid: obj['x-uid'],
            decorator: obj['x-decorator'] || null,
            component: obj['x-component'],
            type: blockType || 'unknown',
            title: obj['x-component-props']?.title || 'Untitled Block',
            path,
            settings: obj['x-settings'],
            toolbar: obj['x-toolbar']
          });
        }

        // 递归查找子属性
        if (obj.properties) {
          Object.keys(obj.properties).forEach(key => {
            findBlocks(obj.properties[key], path ? `${path}.${key}` : key);
          });
        }
      }
    }
    
    findBlocks(schema);
    
    return {
      content: [
        {
          type: 'text',
          text: `Found ${blocks.length} blocks in page:\n${JSON.stringify(blocks, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error listing page blocks: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加表格区块工具
 */
export const addTableBlockTool: Tool = {
  name: 'add_table_block',
  description: 'Add a table block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      collectionName: {
        type: 'string',
        description: 'The name of the collection to display'
      },
      title: {
        type: 'string',
        description: 'The title of the table block'
      },
      dataSource: {
        type: 'string',
        description: 'The data source identifier',
        default: 'main'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid', 'collectionName']
  }
};

export async function handleAddTableBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, collectionName, title, dataSource = 'main', position = 'beforeEnd' } = args;
    
    // 创建表格区块Schema
    const template = BLOCK_TEMPLATES.table;
    if (!template) {
      throw new Error('Table block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title
    });
    
    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);
    
    return {
      content: [
        {
          type: 'text',
          text: `Table block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || collectionName,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating table block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加表单区块工具
 */
export const addFormBlockTool: Tool = {
  name: 'add_form_block',
  description: 'Add a form block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      collectionName: {
        type: 'string',
        description: 'The name of the collection for the form'
      },
      title: {
        type: 'string',
        description: 'The title of the form block'
      },
      type: {
        type: 'string',
        description: 'The type of form',
        enum: ['create', 'update'],
        default: 'create'
      },
      dataSource: {
        type: 'string',
        description: 'The data source identifier',
        default: 'main'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid', 'collectionName']
  }
};

export async function handleAddFormBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, collectionName, title, type = 'create', dataSource = 'main', position = 'beforeEnd' } = args;
    
    // 创建表单区块Schema
    const template = BLOCK_TEMPLATES.form;
    if (!template) {
      throw new Error('Form block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title,
      type
    });
    
    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);
    
    return {
      content: [
        {
          type: 'text',
          text: `Form block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || `${type} form`,
            type,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating form block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加Markdown区块工具
 */
export const addMarkdownBlockTool: Tool = {
  name: 'add_markdown_block',
  description: 'Add a markdown block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      title: {
        type: 'string',
        description: 'The title of the markdown block',
        default: 'Markdown'
      },
      content: {
        type: 'string',
        description: 'The markdown content',
        default: '# Hello World\n\nThis is a markdown block.'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid']
  }
};

export async function handleAddMarkdownBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, title = 'Markdown', content = '# Hello World\n\nThis is a markdown block.', position = 'beforeEnd' } = args;

    // 创建Markdown区块Schema
    const template = BLOCK_TEMPLATES.markdown;
    if (!template) {
      throw new Error('Markdown block template not found');
    }
    const blockSchema = template.createSchema({
      title,
      content
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Markdown block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            title,
            content: content.substring(0, 100) + (content.length > 100 ? '...' : ''),
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating markdown block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加详情区块工具
 */
export const addDetailsBlockTool: Tool = {
  name: 'add_details_block',
  description: 'Add a details block to a page',
  inputSchema: {
    type: 'object',
    properties: {
      parentUid: {
        type: 'string',
        description: 'The UID of the parent container (usually a Grid)'
      },
      collectionName: {
        type: 'string',
        description: 'The name of the collection to display details for'
      },
      title: {
        type: 'string',
        description: 'The title of the details block'
      },
      dataSource: {
        type: 'string',
        description: 'The data source identifier',
        default: 'main'
      },
      position: {
        type: 'string',
        description: 'Position to insert the block',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        default: 'beforeEnd'
      }
    },
    required: ['parentUid', 'collectionName']
  }
};

export async function handleAddDetailsBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, collectionName, title, dataSource = 'main', position = 'beforeEnd' } = args;

    // 创建详情区块Schema
    const template = BLOCK_TEMPLATES.details;
    if (!template) {
      throw new Error('Details block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Details block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || 'Details',
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating details block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加看板区块处理函数
 */
export async function handleAddKanbanBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, collectionName, title, groupField, sortField, dataSource = 'main', position = 'beforeEnd' } = args;

    // 创建看板区块Schema
    const template = BLOCK_TEMPLATES.kanban;
    if (!template) {
      throw new Error('Kanban block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      groupField,
      sortField,
      title
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Kanban block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || 'Kanban',
            groupField,
            sortField,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating kanban block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加列表区块处理函数
 */
export async function handleAddListBlock(client: NocoBaseClient, args: any) {
  try {
    const { parentUid, collectionName, title, rowKey = 'id', templateSchema, dataSource = 'main', position = 'beforeEnd' } = args;

    // 创建列表区块Schema
    const template = BLOCK_TEMPLATES.list;
    if (!template) {
      throw new Error('List block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title,
      rowKey,
      templateSchema
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `List block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || `${collectionName} List`,
            rowKey,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating list block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加网格卡片区块处理函数
 */
export async function handleAddGridCardBlock(client: NocoBaseClient, args: any) {
  try {
    const {
      parentUid,
      collectionName,
      title,
      rowKey = 'id',
      templateSchema,
      columnCount,
      dataSource = 'main',
      position = 'beforeEnd'
    } = args;

    // 创建网格卡片区块Schema
    const template = BLOCK_TEMPLATES.gridCard;
    if (!template) {
      throw new Error('Grid Card block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title,
      rowKey,
      templateSchema,
      columnCount
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Grid Card block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || `${collectionName} Grid Card`,
            rowKey,
            columnCount: columnCount || { xs: 1, sm: 2, md: 3, lg: 4, xl: 4, xxl: 6 },
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating grid card block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加日历区块处理函数
 */
export async function handleAddCalendarBlock(client: NocoBaseClient, args: any) {
  try {
    const {
      parentUid,
      collectionName,
      title,
      fieldNames,
      showLunar = false,
      defaultView = 'month',
      enableQuickCreateEvent = true,
      weekStart = 0,
      dataSource = 'main',
      position = 'beforeEnd'
    } = args;

    // 验证必需的字段名称
    if (!fieldNames || !fieldNames.title || !fieldNames.start) {
      throw new Error('fieldNames.title and fieldNames.start are required for calendar block');
    }

    // 创建日历区块Schema
    const template = BLOCK_TEMPLATES.calendar;
    if (!template) {
      throw new Error('Calendar block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title,
      fieldNames,
      showLunar,
      defaultView,
      enableQuickCreateEvent,
      weekStart
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Calendar block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || `${collectionName} Calendar`,
            fieldNames,
            defaultView,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating calendar block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 添加图表区块处理函数
 */
export async function handleAddChartBlock(client: NocoBaseClient, args: any) {
  try {
    const {
      parentUid,
      collectionName,
      title,
      chartType = 'line',
      config = {},
      dataSource = 'main',
      position = 'beforeEnd'
    } = args;

    // 创建图表区块Schema
    const template = BLOCK_TEMPLATES.chart;
    if (!template) {
      throw new Error('Chart block template not found');
    }
    const blockSchema = template.createSchema({
      collectionName,
      dataSource,
      title,
      chartType,
      config
    });

    // 插入到页面的 Grid 中
    const result = await client.insertBlockToGrid(parentUid, blockSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Chart block created successfully:\n${JSON.stringify({
            uid: blockSchema['x-uid'],
            collection: collectionName,
            title: title || `${collectionName} Chart`,
            chartType,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating chart block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 删除区块工具
 */
export const removeBlockTool: Tool = {
  name: 'remove_block',
  description: 'Remove a block from a page',
  inputSchema: {
    type: 'object',
    properties: {
      blockUid: {
        type: 'string',
        description: 'The UID of the block to remove'
      }
    },
    required: ['blockUid']
  }
};

export async function handleRemoveBlock(client: NocoBaseClient, args: any) {
  try {
    const { blockUid } = args;

    // 删除区块
    await client.deleteBlockSchema(blockUid);

    return {
      content: [
        {
          type: 'text',
          text: `Block removed successfully: ${blockUid}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error removing block: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 列出可用区块类型工具
 */
export const listBlockTypesTool: Tool = {
  name: 'list_block_types',
  description: 'List all available block types and their descriptions',
  inputSchema: {
    type: 'object',
    properties: {},
    required: []
  }
};

export async function handleListBlockTypes(client: NocoBaseClient, args: any) {
  try {
    const blockTypes = Object.values(BLOCK_TEMPLATES).map(template => ({
      type: template.type,
      name: template.name,
      description: template.description,
      requiresCollection: template.requiresCollection
    }));

    return {
      content: [
        {
          type: 'text',
          text: `Available block types:\n${JSON.stringify(blockTypes, null, 2)}`
        }
      ]
    };
  } catch (error: any) {
    return {
      content: [
        {
          type: 'text',
          text: `Error listing block types: ${error.message}`
        }
      ],
      isError: true
    };
  }
}

/**
 * 注册所有区块工具
 */
export async function registerBlockTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 获取页面Schema工具
  server.registerTool(
    'get_page_schema',
    {
      title: 'Get Page Schema',
      description: 'Get the complete schema structure of a page',
      inputSchema: {
        schemaUid: z.string().describe('The UID of the page schema to retrieve')
      }
    },
    async ({ schemaUid }: { schemaUid: string }) => {
      return await handleGetPageSchema(client, { schemaUid });
    }
  );

  // 列出页面区块工具
  server.registerTool(
    'list_page_blocks',
    {
      title: 'List Page Blocks',
      description: 'List all blocks in a page',
      inputSchema: {
        schemaUid: z.string().describe('The UID of the page schema')
      }
    },
    async ({ schemaUid }: { schemaUid: string }) => {
      return await handleListPageBlocks(client, { schemaUid });
    }
  );

  // 添加表格区块工具
  server.registerTool(
    'add_table_block',
    {
      title: 'Add Table Block',
      description: 'Add a table block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection to display'),
        title: z.string().optional().describe('The title of the table block'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddTableBlock(client, args);
    }
  );

  // 添加表单区块工具
  server.registerTool(
    'add_form_block',
    {
      title: 'Add Form Block',
      description: 'Add a form block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the form'),
        title: z.string().optional().describe('The title of the form block'),
        type: z.enum(['create', 'update']).optional().default('create').describe('The type of form'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddFormBlock(client, args);
    }
  );

  // 添加Markdown区块工具
  server.registerTool(
    'add_markdown_block',
    {
      title: 'Add Markdown Block',
      description: 'Add a markdown block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        title: z.string().optional().default('Markdown').describe('The title of the markdown block'),
        content: z.string().optional().default('# Hello World\n\nThis is a markdown block.').describe('The markdown content'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddMarkdownBlock(client, args);
    }
  );

  // 添加详情区块工具
  server.registerTool(
    'add_details_block',
    {
      title: 'Add Details Block',
      description: 'Add a details block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection to display details for'),
        title: z.string().optional().describe('The title of the details block'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddDetailsBlock(client, args);
    }
  );

  // 添加看板区块工具
  server.registerTool(
    'add_kanban_block',
    {
      title: 'Add Kanban Block',
      description: 'Add a kanban block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the kanban'),
        title: z.string().optional().describe('The title of the kanban block'),
        groupField: z.string().describe('The field to group kanban cards by'),
        sortField: z.string().optional().describe('The field to sort kanban cards by'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddKanbanBlock(client, args);
    }
  );

  // 添加列表区块工具
  server.registerTool(
    'add_list_block',
    {
      title: 'Add List Block',
      description: 'Add a list block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the list'),
        title: z.string().optional().describe('The title of the list block'),
        rowKey: z.string().optional().default('id').describe('The field to use as row key'),
        templateSchema: z.any().optional().describe('Custom template schema for list items'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddListBlock(client, args);
    }
  );

  // 添加网格卡片区块工具
  server.registerTool(
    'add_grid_card_block',
    {
      title: 'Add Grid Card Block',
      description: 'Add a grid card block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the grid card'),
        title: z.string().optional().describe('The title of the grid card block'),
        rowKey: z.string().optional().default('id').describe('The field to use as row key'),
        templateSchema: z.any().optional().describe('Custom template schema for card items'),
        columnCount: z.object({
          xs: z.number().optional().default(1).describe('Columns on extra small screens'),
          sm: z.number().optional().default(2).describe('Columns on small screens'),
          md: z.number().optional().default(3).describe('Columns on medium screens'),
          lg: z.number().optional().default(4).describe('Columns on large screens'),
          xl: z.number().optional().default(4).describe('Columns on extra large screens'),
          xxl: z.number().optional().default(6).describe('Columns on extra extra large screens')
        }).optional().describe('Column count configuration for different screen sizes'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddGridCardBlock(client, args);
    }
  );

  // 添加日历区块工具
  server.registerTool(
    'add_calendar_block',
    {
      title: 'Add Calendar Block',
      description: 'Add a calendar block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the calendar'),
        title: z.string().optional().describe('The title of the calendar block'),
        fieldNames: z.object({
          id: z.string().optional().default('id').describe('The ID field name'),
          title: z.string().describe('The field to use as event title'),
          start: z.string().describe('The field to use as event start date/time'),
          end: z.string().optional().describe('The field to use as event end date/time'),
          colorFieldName: z.string().optional().describe('The field to use for event colors')
        }).describe('Field mappings for calendar events'),
        showLunar: z.boolean().optional().default(false).describe('Whether to show lunar calendar'),
        defaultView: z.enum(['month', 'week', 'day']).optional().default('month').describe('Default calendar view'),
        enableQuickCreateEvent: z.boolean().optional().default(true).describe('Whether to enable quick event creation'),
        weekStart: z.number().optional().default(0).describe('First day of week (0=Sunday, 1=Monday)'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddCalendarBlock(client, args);
    }
  );

  // 添加图表区块工具
  server.registerTool(
    'add_chart_block',
    {
      title: 'Add Chart Block',
      description: 'Add a chart block to a page',
      inputSchema: {
        parentUid: z.string().describe('The UID of the parent container (usually a Grid)'),
        collectionName: z.string().describe('The name of the collection for the chart'),
        title: z.string().optional().describe('The title of the chart block'),
        chartType: z.enum(['line', 'bar', 'pie', 'area', 'column', 'scatter', 'doughnut', 'radar']).optional().default('line').describe('The type of chart to display'),
        config: z.object({
          xField: z.string().optional().describe('Field for X-axis'),
          yField: z.string().optional().describe('Field for Y-axis'),
          seriesField: z.string().optional().describe('Field for series grouping'),
          colorField: z.string().optional().describe('Field for color mapping'),
          aggregation: z.enum(['sum', 'count', 'avg', 'max', 'min']).optional().default('count').describe('Aggregation method'),
          sort: z.array(z.object({
            field: z.string(),
            order: z.enum(['asc', 'desc'])
          })).optional().describe('Sort configuration'),
          filter: z.any().optional().describe('Filter conditions')
        }).optional().describe('Chart configuration options'),
        dataSource: z.string().optional().default('main').describe('The data source identifier'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().default('beforeEnd').describe('Position to insert the block')
      }
    },
    async (args: any) => {
      return await handleAddChartBlock(client, args);
    }
  );

  // 删除区块工具
  server.registerTool(
    'remove_block',
    {
      title: 'Remove Block',
      description: 'Remove a block from a page',
      inputSchema: {
        blockUid: z.string().describe('The UID of the block to remove')
      }
    },
    async ({ blockUid }: { blockUid: string }) => {
      return await handleRemoveBlock(client, { blockUid });
    }
  );

  // 列出区块类型工具
  server.registerTool(
    'list_block_types',
    {
      title: 'List Block Types',
      description: 'List all available block types and their descriptions',
      inputSchema: {}
    },
    async () => {
      return await handleListBlockTypes(client, {});
    }
  );
}

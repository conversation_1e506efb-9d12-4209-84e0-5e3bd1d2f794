import { <PERSON>cp<PERSON>erver } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type Role } from "../client.js";

export async function registerRoleTools(server: <PERSON>cp<PERSON>erve<PERSON>, client: NocoBaseClient) {
  // List roles
  server.registerTool(
    "list_roles",
    {
      title: "List Roles",
      description: "List all roles in the NocoBase application with optional filtering, sorting, and pagination",
      inputSchema: {
        page: z.number().optional().describe("Page number for pagination (default: 1)"),
        pageSize: z.number().optional().describe("Number of roles per page (default: 20)"),
        filter: z.record(z.any()).optional().describe("Filter conditions for roles (e.g., {\"hidden\": false})"),
        sort: z.array(z.string()).optional().describe("Sort fields (e.g., [\"createdAt:desc\", \"title:asc\"])"),
        appends: z.array(z.string()).optional().describe("Related fields to include (e.g., [\"users\", \"resources\"])")
      }
    },
    async ({ page, pageSize, filter, sort, appends }) => {
      try {
        const options: any = {};
        if (page !== undefined) options.page = page;
        if (pageSize !== undefined) options.pageSize = pageSize;
        if (filter !== undefined) options.filter = filter;
        if (sort !== undefined) options.sort = sort;
        if (appends !== undefined) options.appends = appends;
        
        const result = await client.listRoles(options);
        
        const summary = `Found ${result.data.length} roles`;
        const metaInfo = result.meta ? 
          `\nPagination: Page ${result.meta.page}/${result.meta.totalPage}, Total: ${result.meta.count}` : '';
        
        return {
          content: [{
            type: "text",
            text: `${summary}${metaInfo}\n\n${JSON.stringify(result.data, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing roles: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get role
  server.registerTool(
    "get_role",
    {
      title: "Get Role",
      description: "Get detailed information about a specific role by name",
      inputSchema: {
        name: z.string().describe("Role name (unique identifier)"),
        appends: z.array(z.string()).optional().describe("Related fields to include (e.g., [\"users\", \"resources\"])")
      }
    },
    async ({ name, appends }) => {
      try {
        const role = await client.getRole(name, appends);
        return {
          content: [{
            type: "text",
            text: `Role details:\n\n${JSON.stringify(role, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create role
  server.registerTool(
    "create_role",
    {
      title: "Create Role",
      description: "Create a new role in the NocoBase application",
      inputSchema: {
        name: z.string().optional().describe("Role unique identifier (auto-generated if not provided)"),
        title: z.string().describe("Role display name"),
        description: z.string().optional().describe("Role description"),
        strategy: z.object({
          actions: z.array(z.string()).describe("Allowed actions (e.g., ['create', 'view', 'update', 'destroy'])")
        }).optional().describe("Role strategy configuration"),
        default: z.boolean().optional().describe("Whether this is a default role"),
        hidden: z.boolean().optional().describe("Whether this role is hidden"),
        allowConfigure: z.boolean().optional().describe("Whether role can configure system"),
        allowNewMenu: z.boolean().optional().describe("Whether role can create new menus"),
        snippets: z.array(z.string()).optional().describe("Permission snippets"),
        color: z.string().optional().describe("Role color"),
        roleData: z.record(z.any()).optional().describe("Additional role data as key-value pairs")
      }
    },
    async ({ name, title, description, strategy, default: isDefault, hidden, allowConfigure, allowNewMenu, snippets, color, roleData }) => {
      try {
        const roleToCreate: Partial<Role> = {
          title,
          ...(name && { name }),
          ...(description && { description }),
          ...(strategy && { strategy }),
          ...(isDefault !== undefined && { default: isDefault }),
          ...(hidden !== undefined && { hidden }),
          ...(allowConfigure !== undefined && { allowConfigure }),
          ...(allowNewMenu !== undefined && { allowNewMenu }),
          ...(snippets && { snippets }),
          ...(color && { color }),
          ...roleData
        };

        const role = await client.createRole(roleToCreate);
        return {
          content: [{
            type: "text",
            text: `Role created successfully:\n\n${JSON.stringify(role, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update role
  server.registerTool(
    "update_role",
    {
      title: "Update Role",
      description: "Update an existing role's information",
      inputSchema: {
        name: z.string().describe("Role name to update"),
        title: z.string().optional().describe("Role display name"),
        description: z.string().optional().describe("Role description"),
        strategy: z.object({
          actions: z.array(z.string()).describe("Allowed actions")
        }).optional().describe("Role strategy configuration"),
        default: z.boolean().optional().describe("Whether this is a default role"),
        hidden: z.boolean().optional().describe("Whether this role is hidden"),
        allowConfigure: z.boolean().optional().describe("Whether role can configure system"),
        allowNewMenu: z.boolean().optional().describe("Whether role can create new menus"),
        snippets: z.array(z.string()).optional().describe("Permission snippets"),
        color: z.string().optional().describe("Role color"),
        roleData: z.record(z.any()).optional().describe("Additional role data to update as key-value pairs")
      }
    },
    async ({ name, title, description, strategy, default: isDefault, hidden, allowConfigure, allowNewMenu, snippets, color, roleData }) => {
      try {
        const updates: Partial<Role> = {
          ...(title && { title }),
          ...(description && { description }),
          ...(strategy && { strategy }),
          ...(isDefault !== undefined && { default: isDefault }),
          ...(hidden !== undefined && { hidden }),
          ...(allowConfigure !== undefined && { allowConfigure }),
          ...(allowNewMenu !== undefined && { allowNewMenu }),
          ...(snippets && { snippets }),
          ...(color && { color }),
          ...roleData
        };

        const role = await client.updateRole(name, updates);
        return {
          content: [{
            type: "text",
            text: `Role updated successfully:\n\n${JSON.stringify(role, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete role
  server.registerTool(
    "delete_role",
    {
      title: "Delete Role",
      description: "Delete a role from the NocoBase application",
      inputSchema: {
        name: z.string().describe("Role name to delete")
      }
    },
    async ({ name }) => {
      try {
        await client.deleteRole(name);
        return {
          content: [{
            type: "text",
            text: `Role '${name}' deleted successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Check role
  server.registerTool(
    "check_role",
    {
      title: "Check Role",
      description: "Check current user's role information and permissions",
      inputSchema: {}
    },
    async () => {
      try {
        const roleInfo = await client.checkRole();
        return {
          content: [{
            type: "text",
            text: `Current role information:\n\n${JSON.stringify(roleInfo, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error checking role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Set default role
  server.registerTool(
    "set_default_role",
    {
      title: "Set Default Role",
      description: "Set a role as the default role for new users",
      inputSchema: {
        name: z.string().describe("Role name to set as default")
      }
    },
    async ({ name }) => {
      try {
        await client.setDefaultRole(name);
        return {
          content: [{
            type: "text",
            text: `Role '${name}' set as default role successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error setting default role: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}

/**
 * NocoBase Row-Level Operations Tools
 * 提供行级别操作和表格行操作按钮管理功能
 */

import type { Tool } from '@modelcontextprotocol/sdk/types.js';
import { NocoBaseClient } from '../client.js';
import { uid } from '../utils.js';

/**
 * 预定义的行操作配置
 */
export const ROW_ACTION_TEMPLATES = {
  // 基础行操作
  view: {
    title: "{{t('View')}}",
    action: 'view',
    icon: 'EyeOutlined',
    type: 'link',
    useProps: '{{ useViewActionProps }}'
  },

  edit: {
    title: "{{t('Edit')}}",
    action: 'update',
    icon: 'EditOutlined',
    type: 'link',
    useProps: '{{ useUpdateActionProps }}'
  },

  delete: {
    title: "{{t('Delete')}}",
    action: 'destroy',
    icon: 'DeleteOutlined',
    type: 'link',
    useProps: '{{ useDestroyActionProps }}',
    confirm: {
      title: "{{t('Delete record')}}",
      content: "{{t('Are you sure you want to delete it?')}}"
    }
  },

  duplicate: {
    title: "{{t('Duplicate')}}",
    action: 'duplicate',
    icon: 'CopyOutlined',
    type: 'link',
    useProps: '{{ useDuplicateActionProps }}'
  },

  // 关联操作
  associate: {
    title: "{{t('Associate')}}",
    action: 'associate',
    icon: 'LinkOutlined',
    type: 'link',
    useProps: '{{ useAssociateActionProps }}'
  },

  disassociate: {
    title: "{{t('Disassociate')}}",
    action: 'disassociate',
    icon: 'DisconnectOutlined',
    type: 'link',
    useProps: '{{ useDisassociateActionProps }}'
  }
} as const;

/**
 * 添加表格行操作列工具
 */
export const addRowActionsColumnTool: Tool = {
  name: 'add_row_actions_column',
  description: 'Add an actions column to a table for row-level operations',
  inputSchema: {
    type: 'object',
    properties: {
      tableUid: {
        type: 'string',
        description: 'The UID of the table block'
      },
      columnTitle: {
        type: 'string',
        description: 'Title of the actions column',
        default: '{{t("Actions")}}'
      },
      width: {
        type: 'number',
        description: 'Width of the actions column',
        default: 120
      },
      fixed: {
        type: 'string',
        enum: ['left', 'right'],
        description: 'Fixed position of the column'
      },
      actions: {
        type: 'array',
        description: 'List of actions to include',
        items: {
          type: 'string',
          enum: ['view', 'edit', 'delete', 'duplicate', 'associate', 'disassociate', 'custom']
        },
        default: ['view', 'edit', 'delete']
      },
      customActions: {
        type: 'array',
        description: 'Custom action configurations',
        items: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Action name' },
            title: { type: 'string', description: 'Action title' },
            action: { type: 'string', description: 'Action identifier' },
            icon: { type: 'string', description: 'Icon name' },
            type: { type: 'string', description: 'Button type' },
            useProps: { type: 'string', description: 'Props hook' },
            confirm: { type: 'object', description: 'Confirmation config' }
          }
        }
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the column'
      }
    },
    required: ['tableUid']
  }
};

/**
 * 添加单个行操作按钮工具
 */
export const addRowActionTool: Tool = {
  name: 'add_row_action',
  description: 'Add a single action button to the table row actions column',
  inputSchema: {
    type: 'object',
    properties: {
      actionsColumnUid: {
        type: 'string',
        description: 'The UID of the actions column'
      },
      actionType: {
        type: 'string',
        enum: ['view', 'edit', 'delete', 'duplicate', 'associate', 'disassociate', 'custom'],
        description: 'Type of action to add'
      },
      customConfig: {
        type: 'object',
        description: 'Custom action configuration (required when actionType is "custom")',
        properties: {
          name: { type: 'string', description: 'Action name' },
          title: { type: 'string', description: 'Action title' },
          action: { type: 'string', description: 'Action identifier' },
          icon: { type: 'string', description: 'Icon name' },
          type: { type: 'string', description: 'Button type' },
          useProps: { type: 'string', description: 'Props hook' },
          confirm: { type: 'object', description: 'Confirmation config' }
        }
      },
      position: {
        type: 'string',
        enum: ['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd'],
        description: 'Position to insert the action'
      }
    },
    required: ['actionsColumnUid', 'actionType']
  }
};

/**
 * 高级记录创建工具
 */
export const createRecordAdvancedTool: Tool = {
  name: 'create_record_advanced',
  description: 'Create a record with advanced options like field control and association updates',
  inputSchema: {
    type: 'object',
    properties: {
      collection: {
        type: 'string',
        description: 'Name of the collection'
      },
      values: {
        type: 'object',
        description: 'Record data to create'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Allowed fields list'
      },
      blacklist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Forbidden fields list'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values'
      }
    },
    required: ['collection', 'values']
  }
};

/**
 * 高级记录更新工具
 */
export const updateRecordAdvancedTool: Tool = {
  name: 'update_record_advanced',
  description: 'Update a record with advanced options like field control and force update',
  inputSchema: {
    type: 'object',
    properties: {
      collection: {
        type: 'string',
        description: 'Name of the collection'
      },
      id: {
        type: ['string', 'number'],
        description: 'ID of the record to update'
      },
      values: {
        type: 'object',
        description: 'Updated data'
      },
      whitelist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Allowed fields list'
      },
      blacklist: {
        type: 'array',
        items: { type: 'string' },
        description: 'Forbidden fields list'
      },
      updateAssociationValues: {
        type: 'boolean',
        description: 'Whether to update association values'
      },
      forceUpdate: {
        type: 'boolean',
        description: 'Force update even if no changes'
      },
      filter: {
        type: 'object',
        description: 'Additional filter conditions'
      }
    },
    required: ['collection', 'id', 'values']
  }
};

/**
 * 关联操作工具
 */
export const associationOperationTool: Tool = {
  name: 'association_operation',
  description: 'Perform association operations (add, remove, set, toggle)',
  inputSchema: {
    type: 'object',
    properties: {
      collection: {
        type: 'string',
        description: 'Name of the source collection'
      },
      sourceId: {
        type: ['string', 'number'],
        description: 'ID of the source record'
      },
      associationField: {
        type: 'string',
        description: 'Name of the association field'
      },
      operation: {
        type: 'string',
        enum: ['add', 'remove', 'set', 'toggle'],
        description: 'Type of association operation'
      },
      filterByTk: {
        type: ['string', 'number'],
        description: 'Target record ID'
      },
      filterByTks: {
        type: 'array',
        description: 'Multiple target record IDs'
      },
      values: {
        type: 'object',
        description: 'Association values'
      }
    },
    required: ['collection', 'sourceId', 'associationField', 'operation']
  }
};

/**
 * 查找或创建工具
 */
export const firstOrCreateTool: Tool = {
  name: 'first_or_create',
  description: 'Find existing record or create new one if not found',
  inputSchema: {
    type: 'object',
    properties: {
      collection: {
        type: 'string',
        description: 'Name of the collection'
      },
      values: {
        type: 'object',
        description: 'Data for finding or creating'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for finding'
      }
    },
    required: ['collection', 'values']
  }
};

/**
 * 更新或创建工具
 */
export const updateOrCreateTool: Tool = {
  name: 'update_or_create',
  description: 'Update existing record or create new one if not found',
  inputSchema: {
    type: 'object',
    properties: {
      collection: {
        type: 'string',
        description: 'Name of the collection'
      },
      values: {
        type: 'object',
        description: 'Data for updating or creating'
      },
      filter: {
        type: 'object',
        description: 'Filter conditions for finding'
      }
    },
    required: ['collection', 'values']
  }
};

// Handler functions

/**
 * 处理添加行操作列
 */
export async function handleAddRowActionsColumn(client: NocoBaseClient, args: any) {
  try {
    const {
      tableUid,
      columnTitle = '{{t("Actions")}}',
      width = 120,
      fixed,
      actions = ['view', 'edit', 'delete'],
      customActions = [],
      position = 'afterEnd'
    } = args;

    // 构建行操作按钮
    const actionProperties: any = {};

    // 添加预定义操作
    actions.forEach((actionType: string) => {
      if (actionType === 'custom') return; // 跳过自定义类型

      const template = ROW_ACTION_TEMPLATES[actionType as keyof typeof ROW_ACTION_TEMPLATES];
      if (template) {
        actionProperties[actionType] = {
          type: 'void',
          title: template.title,
          'x-action': template.action,
          'x-component': 'Action.Link',
          'x-component-props': {
            icon: template.icon,
            type: template.type,
            useProps: template.useProps,
            ...('confirm' in template && template.confirm && { confirm: template.confirm })
          },
          'x-designer': 'Action.Designer'
        };
      }
    });

    // 添加自定义操作
    customActions.forEach((customAction: any) => {
      actionProperties[customAction.name] = {
        type: 'void',
        title: customAction.title,
        'x-action': customAction.action,
        'x-component': 'Action.Link',
        'x-component-props': {
          icon: customAction.icon,
          type: customAction.type,
          useProps: customAction.useProps,
          ...(customAction.confirm && { confirm: customAction.confirm })
        },
        'x-designer': 'Action.Designer'
      };
    });

    // 创建行操作列的 Schema
    const columnSchema = {
      type: 'void',
      title: columnTitle,
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      'x-component-props': {
        width,
        ...(fixed && { fixed })
      },
      properties: {
        actions: {
          type: 'void',
          'x-decorator': 'DndContext',
          'x-component': 'Space',
          'x-component-props': {
            split: '|'
          },
          properties: actionProperties
        }
      }
    };

    const result = await client.insertAdjacentSchema(tableUid, columnSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Row actions column added successfully:\n${JSON.stringify({
            columnTitle,
            width,
            fixed,
            actions: actions.concat(customActions.map((a: any) => a.name)),
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding row actions column: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理添加单个行操作按钮
 */
export async function handleAddRowAction(client: NocoBaseClient, args: any) {
  try {
    const { actionsColumnUid, actionType, customConfig, position = 'beforeEnd' } = args;

    let actionConfig;

    if (actionType === 'custom') {
      if (!customConfig) {
        throw new Error('customConfig is required when actionType is "custom"');
      }
      actionConfig = customConfig;
    } else {
      actionConfig = ROW_ACTION_TEMPLATES[actionType as keyof typeof ROW_ACTION_TEMPLATES];
      if (!actionConfig) {
        throw new Error(`Unknown action type: ${actionType}`);
      }
    }

    // 创建操作按钮 Schema
    const actionSchema = {
      type: 'void',
      title: actionConfig.title,
      'x-action': actionConfig.action,
      'x-component': 'Action.Link',
      'x-component-props': {
        icon: actionConfig.icon,
        type: actionConfig.type,
        useProps: actionConfig.useProps,
        ...(actionConfig.confirm && { confirm: actionConfig.confirm })
      },
      'x-designer': 'Action.Designer'
    };

    const result = await client.insertAdjacentSchema(actionsColumnUid, actionSchema, position);

    return {
      content: [
        {
          type: 'text',
          text: `Row action "${actionConfig.title}" added successfully:\n${JSON.stringify({
            actionType,
            title: actionConfig.title,
            action: actionConfig.action,
            position
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error adding row action: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理高级记录创建
 */
export async function handleCreateRecordAdvanced(client: NocoBaseClient, args: any) {
  try {
    const { collection, values, whitelist, blacklist, updateAssociationValues } = args;

    const result = await client.createRecordAdvanced(collection, {
      values,
      whitelist,
      blacklist,
      updateAssociationValues
    });

    return {
      content: [
        {
          type: 'text',
          text: `Record created successfully in '${collection}':\n${JSON.stringify({
            id: result.id,
            whitelist,
            blacklist,
            updateAssociationValues
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error creating record in '${args.collection}': ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理高级记录更新
 */
export async function handleUpdateRecordAdvanced(client: NocoBaseClient, args: any) {
  try {
    const {
      collection,
      id,
      values,
      whitelist,
      blacklist,
      updateAssociationValues,
      forceUpdate,
      filter
    } = args;

    const result = await client.updateRecordAdvanced(collection, id, {
      values,
      whitelist,
      blacklist,
      updateAssociationValues,
      forceUpdate,
      filter
    });

    return {
      content: [
        {
          type: 'text',
          text: `Record updated successfully in '${collection}':\n${JSON.stringify({
            id,
            whitelist,
            blacklist,
            updateAssociationValues,
            forceUpdate,
            filter
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error updating record ${args.id} in '${args.collection}': ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理关联操作
 */
export async function handleAssociationOperation(client: NocoBaseClient, args: any) {
  try {
    const {
      collection,
      sourceId,
      associationField,
      operation,
      filterByTk,
      filterByTks,
      values
    } = args;

    const options: any = {};
    if (filterByTk) options.filterByTk = filterByTk;
    if (filterByTks) options.filterByTks = filterByTks;
    if (values) options.values = values;

    switch (operation) {
      case 'add':
        await client.addAssociation(collection, sourceId, associationField, options);
        break;
      case 'remove':
        await client.removeAssociation(collection, sourceId, associationField, options);
        break;
      case 'set':
        await client.setAssociation(collection, sourceId, associationField, options);
        break;
      case 'toggle':
        await client.toggleAssociation(collection, sourceId, associationField, options);
        break;
      default:
        throw new Error(`Unknown operation: ${operation}`);
    }

    return {
      content: [
        {
          type: 'text',
          text: `Association operation '${operation}' completed successfully:\n${JSON.stringify({
            collection,
            sourceId,
            associationField,
            operation,
            options
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error performing association operation: ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理查找或创建
 */
export async function handleFirstOrCreate(client: NocoBaseClient, args: any) {
  try {
    const { collection, values, filter } = args;

    const result = await client.firstOrCreate(collection, { values, filter });

    return {
      content: [
        {
          type: 'text',
          text: `First or create operation completed in '${collection}':\n${JSON.stringify({
            id: result.id,
            values,
            filter
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error in first or create operation for '${args.collection}': ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 处理更新或创建
 */
export async function handleUpdateOrCreate(client: NocoBaseClient, args: any) {
  try {
    const { collection, values, filter } = args;

    const result = await client.updateOrCreate(collection, { values, filter });

    return {
      content: [
        {
          type: 'text',
          text: `Update or create operation completed in '${collection}':\n${JSON.stringify({
            id: result.id,
            values,
            filter
          }, null, 2)}`
        }
      ]
    };
  } catch (error) {
    return {
      content: [
        {
          type: 'text',
          text: `Error in update or create operation for '${args.collection}': ${error instanceof Error ? error.message : String(error)}`
        }
      ]
    };
  }
}

/**
 * 注册所有行级别操作工具
 */
export async function registerRowOperationTools(server: any, client: NocoBaseClient) {
  const { z } = await import('zod');

  // 添加行操作列
  server.registerTool(
    'add_row_actions_column',
    {
      title: 'Add Row Actions Column',
      description: 'Add an actions column to a table for row-level operations',
      inputSchema: {
        tableUid: z.string().describe('The UID of the table block'),
        columnTitle: z.string().optional().default('{{t("Actions")}}').describe('Title of the actions column'),
        width: z.number().optional().default(120).describe('Width of the actions column'),
        fixed: z.enum(['left', 'right']).optional().describe('Fixed position of the column'),
        actions: z.array(z.enum(['view', 'edit', 'delete', 'duplicate', 'associate', 'disassociate', 'custom']))
          .optional().default(['view', 'edit', 'delete']).describe('List of actions to include'),
        customActions: z.array(z.object({
          name: z.string().describe('Action name'),
          title: z.string().describe('Action title'),
          action: z.string().describe('Action identifier'),
          icon: z.string().describe('Icon name'),
          type: z.string().describe('Button type'),
          useProps: z.string().describe('Props hook'),
          confirm: z.object({}).optional().describe('Confirmation config')
        })).optional().describe('Custom action configurations'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().describe('Position to insert the column')
      }
    },
    async (args: any) => {
      return await handleAddRowActionsColumn(client, args);
    }
  );

  // 添加单个行操作按钮
  server.registerTool(
    'add_row_action',
    {
      title: 'Add Row Action',
      description: 'Add a single action button to the table row actions column',
      inputSchema: {
        actionsColumnUid: z.string().describe('The UID of the actions column'),
        actionType: z.enum(['view', 'edit', 'delete', 'duplicate', 'associate', 'disassociate', 'custom']).describe('Type of action to add'),
        customConfig: z.object({
          name: z.string().describe('Action name'),
          title: z.string().describe('Action title'),
          action: z.string().describe('Action identifier'),
          icon: z.string().describe('Icon name'),
          type: z.string().describe('Button type'),
          useProps: z.string().describe('Props hook'),
          confirm: z.object({}).optional().describe('Confirmation config')
        }).optional().describe('Custom action configuration (required when actionType is "custom")'),
        position: z.enum(['beforeBegin', 'afterBegin', 'beforeEnd', 'afterEnd']).optional().describe('Position to insert the action')
      }
    },
    async (args: any) => {
      return await handleAddRowAction(client, args);
    }
  );

  // 高级记录创建
  server.registerTool(
    'create_record_advanced',
    {
      title: 'Create Record Advanced',
      description: 'Create a record with advanced options like field control and association updates',
      inputSchema: {
        collection: z.string().describe('Name of the collection'),
        values: z.any().describe('Record data to create'),
        whitelist: z.array(z.string()).optional().describe('Allowed fields list'),
        blacklist: z.array(z.string()).optional().describe('Forbidden fields list'),
        updateAssociationValues: z.boolean().optional().describe('Whether to update association values')
      }
    },
    async (args: any) => {
      return await handleCreateRecordAdvanced(client, args);
    }
  );

  // 高级记录更新
  server.registerTool(
    'update_record_advanced',
    {
      title: 'Update Record Advanced',
      description: 'Update a record with advanced options like field control and force update',
      inputSchema: {
        collection: z.string().describe('Name of the collection'),
        id: z.union([z.string(), z.number()]).describe('ID of the record to update'),
        values: z.any().describe('Updated data'),
        whitelist: z.array(z.string()).optional().describe('Allowed fields list'),
        blacklist: z.array(z.string()).optional().describe('Forbidden fields list'),
        updateAssociationValues: z.boolean().optional().describe('Whether to update association values'),
        forceUpdate: z.boolean().optional().describe('Force update even if no changes'),
        filter: z.any().optional().describe('Additional filter conditions')
      }
    },
    async (args: any) => {
      return await handleUpdateRecordAdvanced(client, args);
    }
  );

  // 关联操作
  server.registerTool(
    'association_operation',
    {
      title: 'Association Operation',
      description: 'Perform association operations (add, remove, set, toggle)',
      inputSchema: {
        collection: z.string().describe('Name of the source collection'),
        sourceId: z.union([z.string(), z.number()]).describe('ID of the source record'),
        associationField: z.string().describe('Name of the association field'),
        operation: z.enum(['add', 'remove', 'set', 'toggle']).describe('Type of association operation'),
        filterByTk: z.union([z.string(), z.number()]).optional().describe('Target record ID'),
        filterByTks: z.array(z.any()).optional().describe('Multiple target record IDs'),
        values: z.any().optional().describe('Association values')
      }
    },
    async (args: any) => {
      return await handleAssociationOperation(client, args);
    }
  );

  // 查找或创建
  server.registerTool(
    'first_or_create',
    {
      title: 'First or Create',
      description: 'Find existing record or create new one if not found',
      inputSchema: {
        collection: z.string().describe('Name of the collection'),
        values: z.any().describe('Data for finding or creating'),
        filter: z.any().optional().describe('Filter conditions for finding')
      }
    },
    async (args: any) => {
      return await handleFirstOrCreate(client, args);
    }
  );

  // 更新或创建
  server.registerTool(
    'update_or_create',
    {
      title: 'Update or Create',
      description: 'Update existing record or create new one if not found',
      inputSchema: {
        collection: z.string().describe('Name of the collection'),
        values: z.any().describe('Data for updating or creating'),
        filter: z.any().optional().describe('Filter conditions for finding')
      }
    },
    async (args: any) => {
      return await handleUpdateOrCreate(client, args);
    }
  );
}

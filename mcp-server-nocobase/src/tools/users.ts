import { <PERSON>cp<PERSON>erver } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type User } from "../client.js";

export async function registerUserTools(server: <PERSON>cpServer, client: NocoBaseClient) {
  // List users
  server.registerTool(
    "list_users",
    {
      title: "List Users",
      description: "List all users in the NocoBase application with optional filtering, sorting, and pagination",
      inputSchema: {
        page: z.number().optional().describe("Page number for pagination (default: 1)"),
        pageSize: z.number().optional().describe("Number of users per page (default: 20)"),
        filter: z.record(z.any()).optional().describe("Filter conditions for users (e.g., {\"email.$like\": \"@example.com\"})"),
        sort: z.array(z.string()).optional().describe("Sort fields (e.g., [\"createdAt:desc\", \"nickname:asc\"])"),
        appends: z.array(z.string()).optional().describe("Related fields to include (e.g., [\"roles\", \"createdBy\"])")
      }
    },
    async ({ page, pageSize, filter, sort, appends }) => {
      try {
        const options: any = {};
        if (page !== undefined) options.page = page;
        if (pageSize !== undefined) options.pageSize = pageSize;
        if (filter !== undefined) options.filter = filter;
        if (sort !== undefined) options.sort = sort;
        if (appends !== undefined) options.appends = appends;

        const result = await client.listUsers(options);
        
        const summary = `Found ${result.data.length} users`;
        const metaInfo = result.meta ? 
          `\nPagination: Page ${result.meta.page}/${result.meta.totalPage}, Total: ${result.meta.count}` : '';
        
        return {
          content: [{
            type: "text",
            text: `${summary}${metaInfo}\n\n${JSON.stringify(result.data, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing users: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Get user
  server.registerTool(
    "get_user",
    {
      title: "Get User",
      description: "Get detailed information about a specific user by ID",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("User ID"),
        appends: z.array(z.string()).optional().describe("Related fields to include (e.g., [\"roles\", \"createdBy\"])")
      }
    },
    async ({ id, appends }) => {
      try {
        const user = await client.getUser(id, appends);
        return {
          content: [{
            type: "text",
            text: `User details:\n\n${JSON.stringify(user, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting user: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create user
  server.registerTool(
    "create_user",
    {
      title: "Create User",
      description: "Create a new user in the NocoBase application",
      inputSchema: {
        nickname: z.string().optional().describe("User's display name"),
        username: z.string().optional().describe("User's login username"),
        email: z.string().email().optional().describe("User's email address"),
        phone: z.string().optional().describe("User's phone number"),
        password: z.string().optional().describe("User's password"),
        userData: z.record(z.any()).optional().describe("Additional user data as key-value pairs")
      }
    },
    async ({ nickname, username, email, phone, password, userData }) => {
      try {
        const userToCreate: Partial<User> = {
          ...(nickname && { nickname }),
          ...(username && { username }),
          ...(email && { email }),
          ...(phone && { phone }),
          ...(password && { password }),
          ...userData
        };

        const user = await client.createUser(userToCreate);
        return {
          content: [{
            type: "text",
            text: `User created successfully:\n\n${JSON.stringify(user, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating user: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update user
  server.registerTool(
    "update_user",
    {
      title: "Update User",
      description: "Update an existing user's information",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("User ID to update"),
        nickname: z.string().optional().describe("User's display name"),
        username: z.string().optional().describe("User's login username"),
        email: z.string().email().optional().describe("User's email address"),
        phone: z.string().optional().describe("User's phone number"),
        password: z.string().optional().describe("User's new password"),
        userData: z.record(z.any()).optional().describe("Additional user data to update as key-value pairs")
      }
    },
    async ({ id, nickname, username, email, phone, password, userData }) => {
      try {
        const updates: Partial<User> = {
          ...(nickname && { nickname }),
          ...(username && { username }),
          ...(email && { email }),
          ...(phone && { phone }),
          ...(password && { password }),
          ...userData
        };

        const user = await client.updateUser(id, updates);
        return {
          content: [{
            type: "text",
            text: `User updated successfully:\n\n${JSON.stringify(user, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating user: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Delete user
  server.registerTool(
    "delete_user",
    {
      title: "Delete User",
      description: "Delete a user from the NocoBase application",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("User ID to delete")
      }
    },
    async ({ id }) => {
      try {
        await client.deleteUser(id);
        return {
          content: [{
            type: "text",
            text: `User with ID ${id} deleted successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting user: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}

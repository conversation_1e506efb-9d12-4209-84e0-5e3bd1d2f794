#!/usr/bin/env node

/**
 * 完整的表格管理示例
 * 
 * 演示如何使用表格操作和行级别操作工具来创建一个完整的用户管理表格
 */

import { NocoBaseClient } from '../dist/client.js';

// 配置 NocoBase 客户端
const client = new NocoBaseClient({
  baseUrl: 'https://app.dev.orb.local/api',
  token: 'your-token-here',
  app: 'your-app-name'
});

async function createCompleteUserTable() {
  console.log('🎯 创建完整的用户管理表格\n');

  // 假设我们有一个表格区块的 UID
  const tableUid = 'users_table_uid_here';

  try {
    console.log('📋 步骤 1: 添加表格级别操作按钮');
    
    // 1. 添加新增按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Add User')}}",
      action: 'create',
      icon: 'PlusOutlined',
      type: 'primary',
      align: 'right',
      requiresACL: true,
      aclAction: 'create'
    });
    console.log('✅ 新增用户按钮已添加');

    // 2. 添加批量删除按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Delete Selected')}}",
      action: 'destroy',
      icon: 'DeleteOutlined',
      align: 'right',
      requiresACL: true,
      aclAction: 'destroyMany'
    });
    console.log('✅ 批量删除按钮已添加');

    // 3. 添加导出按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Export Users')}}",
      action: 'export',
      icon: 'ExportOutlined',
      align: 'right',
      requiresACL: false
    });
    console.log('✅ 导出按钮已添加');

    // 4. 添加自定义的重置密码按钮
    await client.addTableAction(tableUid, {
      title: "{{t('Reset Passwords')}}",
      action: 'resetPasswords',
      icon: 'KeyOutlined',
      type: 'default',
      align: 'right',
      requiresACL: true,
      aclAction: 'resetPassword',
      componentProps: {
        confirm: {
          title: "{{t('Reset passwords for selected users?')}}",
          content: "{{t('This will send password reset emails to selected users.')}}"
        }
      }
    });
    console.log('✅ 重置密码按钮已添加');

    console.log('\n📊 步骤 2: 配置表格列');

    // 5. 配置用户名列
    await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      properties: {
        username: {
          type: 'string',
          'x-component': 'CollectionField',
          'x-read-pretty': true,
          title: '用户名'
        }
      },
      title: '用户名',
      width: 150,
      fixed: 'left',
      sortable: true
    });
    console.log('✅ 用户名列已配置');

    // 6. 配置邮箱列
    await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      properties: {
        email: {
          type: 'string',
          'x-component': 'Input.Email',
          'x-read-pretty': true,
          title: '邮箱'
        }
      },
      title: '邮箱地址',
      width: 200,
      sortable: true,
      filterable: true
    });
    console.log('✅ 邮箱列已配置');

    // 7. 配置状态列
    await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      properties: {
        status: {
          type: 'string',
          'x-component': 'Select',
          'x-read-pretty': true,
          title: '状态',
          enum: [
            { label: '活跃', value: 'active', color: 'green' },
            { label: '禁用', value: 'disabled', color: 'red' },
            { label: '待激活', value: 'pending', color: 'orange' }
          ]
        }
      },
      title: '状态',
      width: 100,
      sortable: true,
      filterable: true
    });
    console.log('✅ 状态列已配置');

    console.log('\n🔧 步骤 3: 添加行操作列');

    // 8. 添加行操作列
    const actionsColumnResult = await client.insertAdjacentSchema(tableUid, {
      type: 'void',
      title: '{{t("Actions")}}',
      'x-decorator': 'TableV2.Column.Decorator',
      'x-component': 'TableV2.Column',
      'x-component-props': {
        width: 180,
        fixed: 'right'
      },
      properties: {
        actions: {
          type: 'void',
          'x-decorator': 'DndContext',
          'x-component': 'Space',
          'x-component-props': {
            split: '|'
          },
          properties: {
            view: {
              type: 'void',
              title: "{{t('View')}}",
              'x-action': 'view',
              'x-component': 'Action.Link',
              'x-component-props': {
                icon: 'EyeOutlined',
                type: 'link',
                useProps: '{{ useViewActionProps }}'
              },
              'x-designer': 'Action.Designer'
            },
            edit: {
              type: 'void',
              title: "{{t('Edit')}}",
              'x-action': 'update',
              'x-component': 'Action.Link',
              'x-component-props': {
                icon: 'EditOutlined',
                type: 'link',
                useProps: '{{ useUpdateActionProps }}'
              },
              'x-designer': 'Action.Designer'
            },
            delete: {
              type: 'void',
              title: "{{t('Delete')}}",
              'x-action': 'destroy',
              'x-component': 'Action.Link',
              'x-component-props': {
                icon: 'DeleteOutlined',
                type: 'link',
                useProps: '{{ useDestroyActionProps }}',
                confirm: {
                  title: "{{t('Delete record')}}",
                  content: "{{t('Are you sure you want to delete it?')}}"
                }
              },
              'x-designer': 'Action.Designer'
            },
            resetPassword: {
              type: 'void',
              title: "{{t('Reset Password')}}",
              'x-action': 'resetPassword',
              'x-component': 'Action.Link',
              'x-component-props': {
                icon: 'KeyOutlined',
                type: 'link',
                useProps: '{{ useResetPasswordActionProps }}',
                confirm: {
                  title: "{{t('Reset password')}}",
                  content: "{{t('Send password reset email to this user?')}}"
                }
              },
              'x-designer': 'Action.Designer'
            }
          }
        }
      }
    });
    console.log('✅ 行操作列已添加');

    console.log('\n⚙️ 步骤 4: 配置表格功能');

    // 9. 配置筛选器
    await client.patchSchema(tableUid, {
      'x-decorator-props': {
        params: {
          filter: {
            status: { $ne: 'deleted' } // 默认不显示已删除用户
          }
        }
      },
      'x-enable-quick-filter': true,
      'x-quick-filter-fields': ['username', 'email', 'nickname']
    });
    console.log('✅ 筛选器已配置');

    // 10. 配置排序
    await client.patchSchema(tableUid, {
      'x-decorator-props': {
        params: {
          sort: ['-createdAt'] // 默认按创建时间倒序
        },
        dragSort: false // 禁用拖拽排序
      }
    });
    console.log('✅ 排序已配置');

    console.log('\n🎉 用户管理表格创建完成！');
    console.log('\n📝 功能总结：');
    console.log('表格级别操作：');
    console.log('- ✅ 新增用户按钮');
    console.log('- ✅ 批量删除按钮');
    console.log('- ✅ 导出用户按钮');
    console.log('- ✅ 批量重置密码按钮');
    console.log('\n表格列配置：');
    console.log('- ✅ 用户名列（固定左侧，可排序）');
    console.log('- ✅ 邮箱列（可排序、可筛选）');
    console.log('- ✅ 状态列（可排序、可筛选）');
    console.log('\n行级别操作：');
    console.log('- ✅ 查看用户详情');
    console.log('- ✅ 编辑用户信息');
    console.log('- ✅ 删除用户');
    console.log('- ✅ 重置用户密码');
    console.log('\n表格功能：');
    console.log('- ✅ 快速筛选（用户名、邮箱、昵称）');
    console.log('- ✅ 默认筛选（排除已删除用户）');
    console.log('- ✅ 默认排序（按创建时间倒序）');

  } catch (error) {
    console.error('❌ 创建过程中出现错误：', error.message);
    console.error('💡 提示：请确保提供正确的表格 UID 和 NocoBase 配置');
  }
}

// 演示高级记录操作
async function demonstrateAdvancedRecordOperations() {
  console.log('\n🔬 演示高级记录操作\n');

  try {
    // 1. 高级记录创建
    console.log('1️⃣ 创建用户（仅允许基本字段）');
    const newUser = await client.createRecordAdvanced('users', {
      values: {
        username: 'demo_user',
        email: '<EMAIL>',
        nickname: 'Demo User',
        password: 'should_be_ignored' // 这个字段会被忽略
      },
      whitelist: ['username', 'email', 'nickname'], // 只允许这些字段
      updateAssociationValues: false
    });
    console.log(`✅ 用户创建成功，ID: ${newUser.id}`);

    // 2. 高级记录更新
    console.log('\n2️⃣ 更新用户（禁止修改敏感字段）');
    await client.updateRecordAdvanced('users', newUser.id, {
      values: {
        nickname: 'Updated Demo User',
        password: 'should_be_ignored', // 这个字段会被忽略
        role: 'admin' // 这个字段也会被忽略
      },
      blacklist: ['password', 'role'], // 禁止修改这些字段
      forceUpdate: true
    });
    console.log('✅ 用户更新成功');

    // 3. 关联操作
    console.log('\n3️⃣ 为用户分配角色');
    await client.addAssociation('users', newUser.id, 'roles', {
      filterByTk: 2 // 假设角色 ID 为 2
    });
    console.log('✅ 角色分配成功');

    // 4. 查找或创建
    console.log('\n4️⃣ 查找或创建用户');
    const foundOrCreated = await client.firstOrCreate('users', {
      values: {
        username: 'unique_user',
        email: '<EMAIL>'
      },
      filter: {
        username: 'unique_user'
      }
    });
    console.log(`✅ 用户处理完成，ID: ${foundOrCreated.id}`);

    console.log('\n🎉 高级记录操作演示完成！');

  } catch (error) {
    console.error('❌ 演示过程中出现错误：', error.message);
    console.log('💡 这是正常的，因为我们使用的是示例数据');
  }
}

// 使用说明
console.log(`
📖 完整表格管理示例

本示例演示如何使用 NocoBase MCP 工具创建一个功能完整的用户管理表格：

1. 表格级别操作：新增、批量删除、导出、自定义操作
2. 表格列配置：用户名、邮箱、状态列的显示和行为
3. 行级别操作：查看、编辑、删除、重置密码
4. 表格功能：筛选、排序、快速搜索
5. 高级记录操作：字段控制、关联管理、查找或创建

使用方法：
1. 修改 NocoBase 客户端配置
2. 替换 tableUid 为实际的表格区块 UID
3. 运行示例：node examples/complete-table-management.js

这个示例展示了 MCP 工具的强大功能，让 AI 能够完全控制 NocoBase 表格的各个方面。
`);

// 如果直接运行此文件，则执行演示
if (import.meta.url === `file://${process.argv[1]}`) {
  createCompleteUserTable()
    .then(() => demonstrateAdvancedRecordOperations())
    .catch(console.error);
}

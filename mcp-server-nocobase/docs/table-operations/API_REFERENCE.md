# NocoBase 表格控件操作 API 对照表

基于 NocoBase 源码深入分析，整理了表格控件上所有操作及其对应的 API。

## 1. 表格级别操作 (Table Actions)

| 操作名称 | 组件名称 | x-action | 权限控制 | 主要功能 |
|---------|---------|----------|----------|----------|
| **筛选/搜索** | `FilterActionInitializer` | `filter` | - | 数据筛选和搜索 |
| **新增** | `CreateActionInitializer` | `create` | `create` | 创建新记录 |
| **关联** | `AssociateActionInitializer` | `associate` | `update` | 关联其他数据 |
| **弹窗** | `PopupActionInitializer` | `popup` | - | 打开弹窗 |
| **批量删除** | `BulkDestroyActionInitializer` | `destroy` | `destroyMany` | 批量删除记录 |
| **刷新** | `RefreshActionInitializer` | `refresh` | - | 刷新表格数据 |
| **链接** | `LinkActionInitializer` | `link` | - | 跳转链接 |
| **展开/折叠** | `ExpandableActionInitializer` | `toggle` | - | 树形表格展开/折叠 |
| **自定义请求** | `CustomRequestInitializer` | `customize:table:request:global` | - | 自定义API请求 |

## 2. 行级别操作 (Row Actions)

| 操作名称 | 组件名称 | x-action | 权限控制 | 主要功能 |
|---------|---------|----------|----------|----------|
| **查看** | `ViewActionInitializer` | `view` | `get` | 查看记录详情 |
| **编辑** | `UpdateActionInitializer` | `update` | `update` | 编辑记录 |
| **删除** | `DestroyActionInitializer` | `destroy` | `destroy` | 删除记录 |
| **解除关联** | `DisassociateActionInitializer` | `disassociate` | `update` | 解除关联关系 |
| **添加子项** | `CreateChildInitializer` | `create` | `create` | 创建子记录（树形表） |
| **弹窗** | `PopupActionInitializer` | `popup` | - | 打开弹窗 |
| **更新记录** | `UpdateRecordActionInitializer` | `update` | `update` | 快速更新记录 |
| **自定义请求** | `CustomRequestInitializer` | `customize:table:request` | - | 自定义API请求 |
| **链接** | `LinkActionInitializer` | `link` | - | 跳转链接 |

## 3. 字段级别操作 (Field Operations)

| 操作名称 | 配置项 | 主要功能 |
|---------|--------|----------|
| **自定义列标题** | `customColumnTitle` | 修改列标题 |
| **编辑提示** | `editTooltip` | 设置列提示信息 |
| **列宽度** | `columnWidth` | 调整列宽度 |
| **排序** | `sortable` | 启用/禁用列排序 |
| **固定列** | `fixed` | 左固定/右固定/不固定 |
| **隐藏列** | `hidden` | 隐藏/显示列 |
| **必填** | `required` | 设置字段必填 |
| **模式** | `pattern` | 编辑/只读/易读模式 |
| **默认值** | `setDefaultValue` | 设置字段默认值 |
| **样式** | `style` | 字段样式设置 |

## 4. 核心 API 配置属性

### 表格级别操作配置
```typescript
{
  'x-action': 'create',           // 操作类型
  'x-acl-action': 'create',       // 权限控制
  'x-component': 'Action',        // 组件类型
  'x-align': 'right',            // 对齐方式
  'x-decorator': 'ACLActionProvider', // 权限装饰器
  'x-settings': 'actionSettings:addNew', // 设置项
  'x-component-props': {         // 组件属性
    type: 'primary',
    icon: 'PlusOutlined'
  }
}
```

### 行级别操作配置
```typescript
{
  'x-action': 'update',           // 操作类型
  'x-acl-action': 'update',       // 权限控制
  'x-component': 'Action.Link',   // 链接组件
  'x-decorator': 'ACLActionProvider', // 权限装饰器
  'x-settings': 'actionSettings:edit', // 设置项
  'x-component-props': {         // 组件属性
    icon: 'EditOutlined',
    openMode: 'drawer'           // 打开模式
  }
}
```

### 字段级别配置
```typescript
{
  'x-component': 'TableV2.Column',    // 列组件
  'x-toolbar': 'TableColumnSchemaToolbar', // 工具栏
  'x-settings': 'fieldSettings:TableColumn', // 设置项
  'x-component-props': {              // 组件属性
    width: 100,                       // 列宽度
    fixed: 'left',                    // 固定位置
    sorter: true,                     // 可排序
    tooltip: '提示信息'               // 提示信息
  }
}
```

## 5. 权限控制体系

### 表格操作权限
- `create` - 创建记录
- `update` - 更新记录
- `destroy` - 删除记录
- `destroyMany` - 批量删除记录

### 行操作权限
- `get` - 获取记录详情
- `update` - 更新记录
- `destroy` - 删除记录

### 字段权限
- 通过 `ACLActionProvider` 装饰器控制
- 基于集合和用户角色的访问控制

## 6. 操作组件注册

所有操作都通过 `SchemaInitializer` 体系进行注册和管理：

- 表格操作：`tableActionInitializers`
- 行操作：`tableActionColumnInitializers`
- 字段操作：`tableColumnInitializers`

## 7. 关键源码文件

### 表格操作初始化器
- `packages/core/client/src/schema-initializer/table/TableActionInitializers.tsx`

### 行操作初始化器
- `packages/core/client/src/schema-initializer/table/TableRecordActionInitializers.tsx`

### 字段设置
- `packages/core/client/src/modules/blocks/data-blocks/table/tableColumnSettings.tsx`

### 具体操作组件
- 关联操作：`packages/core/client/src/modules/actions/associate/AssociateActionInitializer.tsx`
- 编辑操作：`packages/core/client/src/modules/actions/view-edit-popup/UpdateActionInitializer.tsx`
- 查看操作：`packages/core/client/src/modules/actions/view-edit-popup/ViewActionInitializer.tsx`
- 删除操作：`packages/core/client/src/modules/actions/delete/DestroyActionInitializer.tsx`

## 8. 扩展机制

### 自定义操作
通过 `SchemaInitializer` 注册自定义操作组件：
```typescript
// 注册自定义表格操作
const customAction = {
  name: 'customAction',
  Component: CustomActionComponent,
  // ... 其他配置
};
```

### 权限扩展
通过 `ACLActionProvider` 实现自定义权限控制：
```typescript
{
  'x-decorator': 'ACLActionProvider',
  'x-acl-action': 'custom:action',
  // ... 其他配置
}
```

这套 API 体系提供了完整的表格操作功能，支持自定义扩展和权限控制。
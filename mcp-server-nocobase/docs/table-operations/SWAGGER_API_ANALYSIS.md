# NocoBase 表格级别操作 Swagger API 调用机制分析报告

## 概述

本报告深入分析 NocoBase 表格级别操作如何通过内置 Swagger API 进行调用，重点关注 `insertAdjacent` 等关键 API 的实现机制和调用流程。

## 1. API 架构概览

NocoBase 使用基于 Resourcer 的 RESTful API 架构，所有表格操作都通过统一的 API 端点进行调用。表格操作的 UI 配置通过 `uiSchemas` API 进行管理，而业务数据操作则通过相应的资源 API 进行。

### 1.1 核心组件关系

```
表格操作配置 → uiSchemas API → UI Schema 存储 → 渲染引擎 → 业务 API
```

## 2. 核心 API 端点定义

### 2.1 UI Schema 相关 API (`/uiSchemas`)

**文件位置**: `/packages/plugins/@nocobase/plugin-ui-schema-storage/src/swagger/index.ts`

#### 主要端点列表

| 方法 | 端点 | 功能描述 |
|------|------|----------|
| POST | `/uiSchemas:insertAdjacent/{uid}` | 在指定位置插入新的 UI Schema 节点 |
| POST | `/uiSchemas:insert` | 插入根节点 |
| POST | `/uiSchemas:patch` | 更新 UI Schema 节点 |
| POST | `/uiSchemas:remove/{uid}` | 删除 UI Schema 节点 |
| GET | `/uiSchemas:getJsonSchema/{uid}` | 获取 JSON Schema |
| GET | `/uiSchemas:getProperties/{uid}` | 获取属性 |

### 2.2 insertAdjacent 方法详解

**API 端点**: `POST /uiSchemas:insertAdjacent/{uid}?position={position}`

#### 参数说明

- `uid` (路径参数): 目标节点的 x-uid
- `position` (查询参数): 插入位置，可选值：
  - `beforeBegin` - 在目标节点之前插入
  - `afterBegin` - 在目标节点开始处插入
  - `beforeEnd` - 在目标节点结束处插入
  - `afterEnd` - 在目标节点之后插入

#### 请求体格式

```json
{
  "schema": {
    "type": "void",
    "title": "新增操作",
    "x-component": "Action",
    "x-action": "create",
    "x-designer": "Action.Designer",
    "x-component-props": {
      "icon": "PlusOutlined",
      "type": "primary"
    },
    "x-decorator": "ACLActionProvider",
    "x-acl-action": "create"
  },
  "wrap": {
    // 可选的包装器 schema
  }
}
```

#### 实际调用示例

```bash
# 示例：在表格操作栏中添加"新增"按钮
curl -X POST "https://app.dev.orb.local/api/uiSchemas:insertAdjacent/2xyni0tk9pb?position=beforeEnd" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "schema": {
      "type": "void",
      "title": "{{t(\"Add new\")}}",
      "x-component": "Action",
      "x-action": "create",
      "x-designer": "Action.Designer",
      "x-component-props": {
        "icon": "PlusOutlined",
        "type": "primary"
      },
      "x-decorator": "ACLActionProvider",
      "x-acl-action": "create",
      "x-settings": "actionSettings:addNew"
    }
  }'
```

## 3. 服务器端实现

### 3.1 Action 处理器

**文件位置**: `/packages/plugins/@nocobase/plugin-ui-schema-storage/src/server/actions/ui-schema-action.ts`

```typescript
export const uiSchemaActions = {
  // 核心插入操作
  insertAdjacent: insertPositionActionBuilder(),
  insertBeforeBegin: insertPositionActionBuilder('beforeBegin'),
  insertAfterBegin: insertPositionActionBuilder('afterBegin'),
  insertBeforeEnd: insertPositionActionBuilder('beforeEnd'),
  insertAfterEnd: insertPositionActionBuilder('afterEnd'),
  
  // 其他操作
  insert: insertActionBuilder(),
  patch: patchActionBuilder(),
  remove: removeActionBuilder(),
  getJsonSchema: getJsonSchemaActionBuilder(),
  getProperties: getPropertiesActionBuilder(),
};
```

### 3.2 Repository 层实现

**文件位置**: `/packages/plugins/@nocobase/plugin-ui-schema-storage/src/server/repository.ts`

核心方法 `insertAdjacent`:

```typescript
async insertAdjacent(
  position: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd',
  target: string,
  schema: any,
  options?: InsertAdjacentOptions,
) {
  const { transaction, removeParentsIfNoChildren, wrap } = options || {};
  
  // 1. 清理缓存
  await this.clearXUidPathCache(schema['x-uid'], transaction);
  
  // 2. 处理包装器逻辑
  if (wrap) {
    const wrapSchema = {
      type: 'void',
      name: `wrap_${Date.now()}`,
      properties: {
        [schema.name]: schema,
      },
    };
    await this.insertAdjacent(position, target, wrapSchema, { ...options, wrap: undefined });
    return;
  }
  
  // 3. 处理schema存在性检查
  const schemaExists = await this.schemaExists(schema, { transaction });
  if (schemaExists) {
    throw new Error('Schema already exists');
  }
  
  // 4. 执行插入操作
  const result = await this[`insert${lodash.upperFirst(position)}`](target, schema, options);
  
  // 5. 触发事件和清理缓存
  await this.emitAfterSaveEvent(result, options);
  await this.clearXUidPathCache(result['x-uid'], transaction);
  
  return result;
}
```

### 3.3 数据库模型

**文件位置**: `/packages/plugins/@nocobase/plugin-ui-schema-storage/src/server/ui-schema-repository.ts`

主要数据表：
- `ui_schemas` - 存储 UI Schema 节点
- `ui_schema_tree_path` - 管理节点间的父子关系

## 4. 客户端 API 调用

### 4.1 API Client 实现

**文件位置**: `/packages/core/client/src/api-client/APIClient.ts`

```typescript
export class APIClient extends APIClientSDK {
  services: Record<string, Result<any, any>> = {};
  silence = false;
  app: Application;
  notification: any = notification;

  getHeaders() {
    const headers = super.getHeaders();
    const appName = this.app?.getName();
    if (appName) {
      headers['X-App'] = appName;
    }
    headers['X-Timezone'] = getCurrentTimezone();
    headers['X-Hostname'] = window?.location?.hostname;
    return headers;
  }

  async request(options) {
    // 自动添加认证头和其他必要的 headers
    const headers = this.getHeaders();
    return this.axios.request({
      ...options,
      headers,
      withCredentials: true,
    });
  }
  
  // 便捷方法
  async resource(name) {
    return new Resource(name, this);
  }

  // 静默模式
  silent() {
    const api = this.cloneInstance();
    api.silence = true;
    return api;
  }
}
```

### 4.2 表格操作初始化器

**文件位置**: `/packages/core/client/src/modules/blocks/data-blocks/table/TableActionInitializers.tsx`

```typescript
const commonOptions = {
  title: "{{t('Configure actions')}}",
  icon: 'SettingOutlined',
  style: {
    marginLeft: 8,
  },
  items: [
    {
      type: 'item',
      name: 'filter',
      title: "{{t('Filter')}}",
      Component: 'FilterActionInitializer',
      schema: {
        'x-align': 'left',
      },
    },
    {
      type: 'item',
      title: "{{t('Add new')}}",
      name: 'addNew',
      Component: 'CreateActionInitializer',
      schema: {
        'x-align': 'right',
        'x-decorator': 'ACLActionProvider',
        'x-acl-action-props': {
          skipScopeCheck: true,
        },
      },
      useVisible: () => useActionAvailable('create'),
    },
    {
      type: 'item',
      title: '{{t("Associate")}}',
      name: 'associate',
      Component: 'AssociateActionInitializer',
      useVisible() {
        const props = useDataBlockProps();
        const collection = useCollection() || ({} as any);
        const { unavailableActions, availableActions } = collection?.options || {};
        if (availableActions) {
          return !!props?.association && availableActions.includes?.('update');
        }
        if (unavailableActions) {
          return !!props?.association && !unavailableActions?.includes?.('update');
        }
        return true;
      },
    },
    {
      type: 'item',
      title: "{{t('Delete')}}",
      name: 'delete',
      Component: 'BulkDestroyActionInitializer',
      schema: {
        'x-align': 'right',
        'x-decorator': 'ACLActionProvider',
      },
      useVisible: () => useActionAvailable('destroyMany'),
    },
    // ... 其他操作
  ],
};

export const tableActionInitializers = new CompatibleSchemaInitializer(
  {
    name: 'table:configureActions',
    ...commonOptions,
  },
  tableActionInitializers_deprecated,
);
```

### 4.3 实际调用示例

```typescript
// 添加表格操作按钮
const addTableAction = async (targetUid, actionSchema) => {
  const apiClient = useAPIClient();
  
  try {
    const response = await apiClient.request({
      url: `/uiSchemas:insertAdjacent/${targetUid}`,
      method: 'POST',
      data: {
        position: 'beforeEnd',
        schema: {
          type: 'void',
          'x-component': 'Action',
          'x-action': actionSchema.action,
          title: actionSchema.title,
          'x-designer': 'Action.Designer',
          'x-component-props': actionSchema.props || {},
          'x-decorator': actionSchema.requiresACL ? 'ACLActionProvider' : undefined,
          'x-acl-action': actionSchema.aclAction,
          'x-settings': actionSchema.settings,
        }
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('Failed to add table action:', error);
    throw error;
  }
};
```

## 5. 表格操作与 API 的映射关系

### 5.1 操作类型映射

| 操作类型 | x-action 值 | HTTP 方法 | API 端点 | 权限控制 |
|---------|-------------|-----------|----------|----------|
| 创建 | `create` | POST | `/{collection}:create` | `create` |
| 更新 | `update` | PUT | `/{collection}:update` | `update` |
| 删除 | `destroy` | DELETE | `/{collection}:destroy` | `destroy` |
| 查看 | `view` | GET | `/{collection}:get` | `get` |
| 批量删除 | `destroy` | POST | `/{collection}:destroyMany` | `destroyMany` |
| 自定义请求 | `customize:table:request:global` | POST | `/customRequests:send/{id}` | 自定义 |
| 筛选 | `filter` | GET | `/{collection}:list` | - |

### 5.2 完整的表格操作配置流程

```typescript
// 1. 定义操作配置
const actionConfig = {
  // 筛选操作
  filter: {
    title: "{{t('Filter')}}",
    action: 'filter',
    component: 'FilterActionInitializer',
    props: {},
    requiresACL: false,
    settings: 'actionSettings:filter',
    align: 'left'
  },
  
  // 新增操作
  create: {
    title: "{{t('Add new')}}",
    action: 'create',
    component: 'CreateActionInitializer',
    props: {
      type: 'primary',
      icon: 'PlusOutlined'
    },
    requiresACL: true,
    aclAction: 'create',
    settings: 'actionSettings:addNew',
    align: 'right'
  },
  
  // 批量删除操作
  bulkDelete: {
    title: "{{t('Delete selected')}}",
    action: 'destroy',
    component: 'BulkDestroyActionInitializer',
    props: {
      icon: 'DeleteOutlined'
    },
    requiresACL: true,
    aclAction: 'destroyMany',
    settings: 'actionSettings:delete',
    align: 'right'
  }
};

// 2. 调用 API 添加操作
const addActionToTable = async (tableUid, actionType) => {
  const config = actionConfig[actionType];
  if (!config) {
    throw new Error(`Unknown action type: ${actionType}`);
  }
  
  const apiClient = useAPIClient();
  
  return await apiClient.request({
    url: `/uiSchemas:insertAdjacent/${tableUid}`,
    method: 'POST',
    data: {
      position: 'beforeEnd',
      schema: {
        type: 'void',
        title: config.title,
        'x-component': 'Action',
        'x-action': config.action,
        'x-designer': 'Action.Designer',
        'x-component-props': config.props,
        'x-decorator': config.requiresACL ? 'ACLActionProvider' : undefined,
        'x-acl-action': config.aclAction,
        'x-settings': config.settings,
      }
    }
  });
};
```

## 6. Swagger API 文档

### 6.1 API 文档生成

**文件位置**: `/packages/plugins/@nocobase/plugin-api-doc/src/server/swagger/`

NocoBase 自动生成 Swagger 文档，可通过 `/api/swagger` 访问。

### 6.2 主要 Schema 定义

```typescript
{
  "uiSchema": {
    "type": "object",
    "properties": {
      "type": {"type": "string"},
      "title": {"type": "string"},
      "x-uid": {"type": "string"},
      "x-index": {"type": "integer"},
      "x-async": {"type": "boolean"},
      "name": {"type": "string"},
      "properties": {
        "type": "object",
        "additionalProperties": {"$ref": "#/components/schemas/uiSchema"}
      }
    }
  },
  "insertAdjacentRequest": {
    "type": "object",
    "properties": {
      "schema": {"$ref": "#/components/schemas/uiSchema"},
      "wrap": {"type": "object"}
    }
  }
}
```

### 6.3 认证和安全

所有 API 调用都需要认证：

```typescript
// 请求头
{
  "Authorization": "Bearer <jwt-token>",
  "Content-Type": "application/json",
  "X-Role": "user-role" // 可选
}
```

## 7. 关键技术要点

### 7.1 树形结构管理

- 使用 `uiSchemaTreePath` 表管理节点间的父子关系
- 支持复杂的树形操作（移动、插入、删除）
- 自动维护节点路径和索引

### 7.2 缓存机制

- 使用 Redis 缓存 UI Schema 结构
- 在修改操作后自动清理相关缓存
- 支持缓存预热和批量操作

### 7.3 事务处理

- 所有数据库操作都在事务中执行
- 支持事务回滚和错误处理
- 确保数据一致性

### 7.4 权限控制

- 通过 ACL 控制 API 访问权限
- 支持基于角色的访问控制
- 细粒度的操作权限控制

## 8. 实际应用示例

### 8.1 动态添加表格操作

```typescript
// 场景：根据用户权限动态添加表格操作
const configureTableActions = async (tableUid, userRole) => {
  const apiClient = useAPIClient();
  const actions = [];
  
  // 基础操作
  actions.push({
    title: "{{t('Filter')}}",
    action: 'filter',
    component: 'FilterActionInitializer',
    align: 'left'
  });
  
  // 根据角色添加操作
  if (userRole === 'admin' || userRole === 'manager') {
    actions.push({
      title: "{{t('Add new')}}",
      action: 'create',
      component: 'CreateActionInitializer',
      props: { type: 'primary', icon: 'PlusOutlined' },
      requiresACL: true,
      aclAction: 'create',
      align: 'right'
    });
  }
  
  // 批量操作
  if (userRole === 'admin') {
    actions.push({
      title: "{{t('Delete selected')}}",
      action: 'destroy',
      component: 'BulkDestroyActionInitializer',
      props: { icon: 'DeleteOutlined' },
      requiresACL: true,
      aclAction: 'destroyMany',
      align: 'right'
    });
  }
  
  // 批量添加操作
  for (const action of actions) {
    await apiClient.request({
      url: `/uiSchemas:insertAdjacent/${tableUid}`,
      method: 'POST',
      data: {
        position: 'beforeEnd',
        schema: {
          type: 'void',
          title: action.title,
          'x-component': 'Action',
          'x-action': action.action,
          'x-designer': 'Action.Designer',
          'x-component-props': action.props || {},
          'x-decorator': action.requiresACL ? 'ACLActionProvider' : undefined,
          'x-acl-action': action.aclAction,
          'x-align': action.align,
        }
      }
    });
  }
};
```

### 8.2 自定义操作处理

**文件位置**: `/packages/plugins/@nocobase/plugin-action-custom-request/src/client/hooks/useCustomizeRequestActionProps.ts`

```typescript
export const useCustomizeRequestActionProps = () => {
  const apiClient = useAPIClient();
  const navigate = useNavigateNoUpdate();
  const actionSchema = useFieldSchema();
  const { field } = useBlockRequestContext();
  const compile = useCompile();
  const form = useForm();
  const { name: blockType } = useBlockContext() || {};
  const recordData = useCollectionRecordData();
  const fieldSchema = useFieldSchema();
  const actionField = useField();
  const { setVisible } = useActionContext();
  const { modal, message } = App.useApp();
  const dataSourceKey = useDataSourceKey();
  const { ctx } = useContextVariable();
  const localVariables = useLocalVariables();
  const variables = useVariables();

  return {
    async onClick(e?, callBack?) {
      const selectedRecord = field?.data?.selectedRowData ? field?.data?.selectedRowData : ctx;
      const { skipValidator, onSuccess } = actionSchema?.['x-action-settings'] ?? {};
      const {
        manualClose,
        redirecting,
        redirectTo: rawRedirectTo,
        successMessage: rawSuccessMessage,
        actionAfterSuccess,
      } = onSuccess || {};
      let successMessage = rawSuccessMessage;
      let redirectTo = rawRedirectTo;
      const xAction = actionSchema?.['x-action'];
      
      if (skipValidator !== true && xAction === 'customize:form:request') {
        await form.submit();
      }

      let currentRecordData = { ...recordData };
      if (xAction === 'customize:form:request') {
        currentRecordData = form.values;
      }

      actionField.data ??= {};
      actionField.data.loading = true;
      try {
        const requestId = fieldSchema['x-custom-request-id'] || fieldSchema['x-uid'];
        const res = await apiClient.request({
          url: `/customRequests:send/${requestId}`,
          method: 'POST',
          data: {
            currentRecord: {
              dataSourceKey,
              data: currentRecordData,
            },
            $nForm: blockType === 'form' ? form.values : undefined,
            $nSelectedRecord: selectedRecord,
          },
          responseType: fieldSchema['x-response-type'] === 'stream' ? 'blob' : 'json',
        });
        
        // 处理文件下载
        if (res.headers['content-disposition']) {
          const contentDisposition = res.headers['content-disposition'];
          const utf8Match = contentDisposition.match(/filename\*=utf-8''([^;]+)/i);
          const asciiMatch = contentDisposition.match(/filename=\"([^\"]+)\"/i);
          if (utf8Match) {
            saveAs(res.data, decodeURIComponent(utf8Match[1]));
          } else if (asciiMatch) {
            saveAs(res.data, asciiMatch[1]);
          }
        }
        
        // 处理成功消息和重定向
        successMessage = await getVariableValue(successMessage, {
          variables,
          localVariables: [
            ...localVariables,
            { name: '$nResponse', ctx: new Proxy({ ...res?.data?.data, ...res?.data }, {}) },
          ],
        });
        
        if (rawRedirectTo) {
          redirectTo = await getVariableValue(rawRedirectTo, {
            variables,
            localVariables,
          });
        }
        
        // 显示成功消息
        if (successMessage) {
          if (manualClose) {
            modal.success({
              title: compile(successMessage),
              onOk: async () => {
                if (redirectTo) {
                  if (isURL(redirectTo)) {
                    window.location.href = redirectTo;
                  } else {
                    navigate(redirectTo);
                  }
                }
              },
            });
          } else {
            message.success(compile(successMessage));
            if (redirectTo) {
              if (isURL(redirectTo)) {
                window.location.href = redirectTo;
              } else {
                navigate(redirectTo);
              }
            }
          }
        }
        
      } finally {
        actionField.data.loading = false;
      }
    },
  };
};
```

## 9. 性能优化建议

### 9.1 批量操作

```typescript
// 批量添加操作以减少 API 调用
const batchAddActions = async (tableUid, actions) => {
  const apiClient = useAPIClient();
  
  // 使用事务确保一致性
  const transaction = await apiClient.request({
    url: '/transactions:start',
    method: 'POST'
  });
  
  try {
    const results = [];
    for (const action of actions) {
      const result = await apiClient.request({
        url: `/uiSchemas:insertAdjacent/${tableUid}`,
        method: 'POST',
        data: {
          position: 'beforeEnd',
          schema: action.schema
        },
        headers: {
          'X-Transaction-ID': transaction.data.id
        }
      });
      results.push(result);
    }
    
    await apiClient.request({
      url: `/transactions:commit/${transaction.data.id}`,
      method: 'POST'
    });
    
    return results;
  } catch (error) {
    await apiClient.request({
      url: `/transactions:rollback/${transaction.data.id}`,
      method: 'POST'
    });
    throw error;
  }
};
```

### 9.2 缓存策略

```typescript
// 使用缓存减少重复调用
const getTableActions = async (tableUid) => {
  const cacheKey = `table_actions_${tableUid}`;
  const cached = localStorage.getItem(cacheKey);
  
  if (cached) {
    return JSON.parse(cached);
  }
  
  const apiClient = useAPIClient();
  const response = await apiClient.request({
    url: `/uiSchemas:getProperties/${tableUid}`,
    method: 'GET'
  });
  
  localStorage.setItem(cacheKey, JSON.stringify(response.data));
  return response.data;
};
```

## 10. 错误处理和调试

### 10.1 常见错误及解决方案

| 错误类型 | 可能原因 | 解决方案 |
|---------|----------|----------|
| 401 Unauthorized | 认证失败 | 检查 JWT token 是否有效 |
| 403 Forbidden | 权限不足 | 检查用户权限配置 |
| 404 Not Found | 节点不存在 | 验证目标 UID 是否正确 |
| 409 Conflict | Schema 已存在 | 检查是否重复添加 |
| 422 Unprocessable Entity | 参数错误 | 验证请求体格式 |

### 10.2 调试工具

```typescript
// 调试 API 调用
const debugApiCall = async (url, options) => {
  console.log('API Request:', {
    url,
    method: options.method,
    data: options.data,
    headers: options.headers
  });
  
  try {
    const response = await apiClient.request(options);
    console.log('API Response:', response);
    return response;
  } catch (error) {
    console.error('API Error:', {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });
    throw error;
  }
};
```

## 11. 总结

NocoBase 表格级别操作通过 Swagger API 调用的机制具有以下特点：

1. **统一的 API 设计**: 所有 UI 操作都通过 `uiSchemas` API 进行管理
2. **灵活的插入机制**: `insertAdjacent` 提供了精确的位置控制
3. **完善的权限控制**: 通过 ACL 实现细粒度的权限管理
4. **高性能**: 支持缓存、事务和批量操作
5. **易于扩展**: 支持自定义操作和组件

开发者可以通过这套 API 机制灵活地配置和扩展表格操作，实现复杂的业务需求。建议在实际开发中遵循最佳实践，合理使用缓存和事务，确保系统的稳定性和性能。

---

**报告日期**: 2025-01-09  
**版本**: NocoBase 最新版本  
**作者**: Claude Code Assistant  

**注意**: 本报告基于 NocoBase 最新源码分析，代码示例已与实际实现保持一致。
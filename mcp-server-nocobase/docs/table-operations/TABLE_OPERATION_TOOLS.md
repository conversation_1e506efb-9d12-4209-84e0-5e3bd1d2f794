# NocoBase 表格操作工具文档

## 概述

本文档介绍了为 NocoBase MCP 服务器新增的表格操作工具集，这些工具基于 [SWAGGER_API_ANALYSIS.md](./SWAGGER_API_ANALYSIS.md) 中的 API 分析实现，提供了完整的表格操作管理功能。

## 新增工具列表

### 1. 表格操作按钮管理

#### 1.1 add_table_action - 添加表格操作按钮

为表格区块添加操作按钮，支持预定义操作类型和自定义配置。

**参数：**
- `tableUid` (string): 表格区块的 UID
- `actionType` (enum): 操作类型
  - `create`: 新增操作
  - `filter`: 筛选操作
  - `bulkDelete`: 批量删除操作
  - `export`: 导出操作
  - `import`: 导入操作
  - `refresh`: 刷新操作
  - `customRequest`: 自定义请求操作
  - `custom`: 自定义操作（需要提供 customConfig）
- `customConfig` (object, 可选): 自定义操作配置
- `position` (enum, 可选): 插入位置

**示例：**
```json
{
  "tableUid": "abc123",
  "actionType": "create"
}
```

#### 1.2 remove_table_action - 删除表格操作按钮

删除指定的表格操作按钮。

**参数：**
- `actionUid` (string): 要删除的操作按钮 UID

#### 1.3 update_table_action - 更新表格操作按钮

更新现有的表格操作按钮配置。

**参数：**
- `actionUid` (string): 操作按钮 UID
- `updates` (object): 更新内容

#### 1.4 list_table_actions - 列出表格操作按钮

列出表格区块中的所有操作按钮。

**参数：**
- `tableUid` (string): 表格区块 UID

### 2. 表格列管理

#### 2.1 configure_table_column - 配置表格列

为表格添加或配置列显示。

**参数：**
- `tableUid` (string): 表格区块 UID
- `fieldName` (string): 字段名称
- `columnConfig` (object): 列配置
  - `title` (string, 可选): 列标题
  - `width` (number, 可选): 列宽度
  - `fixed` (enum, 可选): 固定位置 ('left' | 'right')
  - `sortable` (boolean, 可选): 是否可排序
  - `filterable` (boolean, 可选): 是否可筛选
  - `component` (string, 可选): 显示组件
  - `componentProps` (object, 可选): 组件属性
- `position` (enum, 可选): 插入位置

### 3. 表格配置管理

#### 3.1 configure_table_filter - 配置表格筛选器

配置表格的筛选功能。

**参数：**
- `tableUid` (string): 表格区块 UID
- `filterConfig` (object): 筛选配置
  - `defaultFilter` (object, 可选): 默认筛选条件
  - `filterForm` (object, 可选): 筛选表单 Schema
  - `enableQuickFilter` (boolean, 可选): 启用快速筛选
  - `quickFilterFields` (array, 可选): 快速筛选字段

#### 3.2 configure_table_sort - 配置表格排序

配置表格的排序功能。

**参数：**
- `tableUid` (string): 表格区块 UID
- `sortConfig` (object): 排序配置
  - `defaultSort` (array, 可选): 默认排序字段
  - `enableDragSort` (boolean, 可选): 启用拖拽排序
  - `sortField` (string, 可选): 排序字段名

### 4. 自定义请求

#### 4.1 send_custom_request - 发送自定义请求

从表格上下文发送自定义请求。

**参数：**
- `requestId` (string): 自定义请求 ID
- `requestData` (object, 可选): 请求数据
  - `currentRecord` (object, 可选): 当前记录数据
  - `selectedRecords` (array, 可选): 选中记录数据
  - `formData` (object, 可选): 表单数据

## 预定义操作模板

系统提供了以下预定义的操作模板：

### 基础操作
- **create**: 新增操作，带有主要按钮样式和加号图标
- **filter**: 筛选操作，带有筛选图标，左对齐
- **bulkDelete**: 批量删除操作，带有删除图标和 ACL 权限控制
- **export**: 导出操作，带有导出图标
- **import**: 导入操作，带有导入图标和 ACL 权限控制
- **refresh**: 刷新操作，带有刷新图标
- **customRequest**: 自定义请求操作，带有 API 图标

## 使用示例

### 示例 1：为表格添加新增按钮

```json
{
  "tool": "add_table_action",
  "arguments": {
    "tableUid": "table_users_123",
    "actionType": "create"
  }
}
```

### 示例 2：添加自定义操作按钮

```json
{
  "tool": "add_table_action",
  "arguments": {
    "tableUid": "table_users_123",
    "actionType": "custom",
    "customConfig": {
      "title": "{{t('Send Email')}}",
      "action": "sendEmail",
      "icon": "MailOutlined",
      "type": "default",
      "align": "right",
      "requiresACL": true,
      "aclAction": "sendEmail"
    }
  }
}
```

### 示例 3：配置表格列

```json
{
  "tool": "configure_table_column",
  "arguments": {
    "tableUid": "table_users_123",
    "fieldName": "email",
    "columnConfig": {
      "title": "邮箱地址",
      "width": 200,
      "sortable": true,
      "filterable": true,
      "component": "Input.Email"
    }
  }
}
```

### 示例 4：配置表格筛选器

```json
{
  "tool": "configure_table_filter",
  "arguments": {
    "tableUid": "table_users_123",
    "filterConfig": {
      "defaultFilter": {
        "status": "active"
      },
      "enableQuickFilter": true,
      "quickFilterFields": ["name", "email"]
    }
  }
}
```

## API 实现细节

### 底层 API 调用

这些工具基于以下 NocoBase API 端点实现：

1. **insertAdjacent**: `POST /uiSchemas:insertAdjacent/{uid}?position={position}`
2. **patch**: `POST /uiSchemas:patch`
3. **remove**: `POST /uiSchemas:remove/{uid}`
4. **getProperties**: `GET /uiSchemas:getProperties/{uid}`
5. **customRequest**: `POST /customRequests:send/{id}`

### Schema 结构

操作按钮的基本 Schema 结构：

```typescript
{
  type: 'void',
  title: "{{t('Action Title')}}",
  'x-component': 'Action',
  'x-action': 'actionType',
  'x-designer': 'Action.Designer',
  'x-component-props': {
    icon: 'IconName',
    type: 'primary'
  },
  'x-decorator': 'ACLActionProvider',
  'x-acl-action': 'aclAction',
  'x-settings': 'actionSettings:settingName'
}
```

## 错误处理

所有工具都包含完整的错误处理机制：

- 参数验证
- API 调用错误捕获
- 详细的错误信息返回
- 操作结果确认

## 权限控制

工具支持 NocoBase 的 ACL 权限控制：

- 通过 `requiresACL` 参数控制是否需要权限检查
- 通过 `aclAction` 参数指定具体的权限动作
- 自动添加 `ACLActionProvider` 装饰器

## 扩展性

工具设计具有良好的扩展性：

- 支持自定义操作配置
- 支持自定义组件属性
- 支持自定义 Schema 结构
- 预留扩展接口

## 注意事项

1. 确保目标表格区块存在且 UID 正确
2. 自定义操作需要提供完整的配置信息
3. ACL 权限配置需要与 NocoBase 系统权限设置一致
4. 操作按钮的位置会影响用户体验，建议合理安排
5. 批量操作时注意性能影响

---

**更新日期**: 2025-01-09  
**版本**: v1.0.0  
**作者**: Claude Code Assistant

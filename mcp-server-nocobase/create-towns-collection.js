#!/usr/bin/env node

// 直接使用NocoBase客户端创建镇街集合
import { NocoBaseClient } from './dist/client.js';

async function createTownsCollection() {
  console.log('🚀 开始创建镇街集合...\n');

  // 创建NocoBase客户端
  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 创建镇街集合
    console.log('📋 创建镇街集合');
    const townsCollection = await client.createCollection({
      name: 'towns',
      title: '镇街集合',
      description: '管理镇街信息的集合',
      autoGenId: true,
      createdAt: true,
      updatedAt: true,
      createdBy: false,
      updatedBy: false
    });
    console.log(`✅ 成功创建镇街集合: ${townsCollection.name} (${townsCollection.title})`);
    console.log();

    // 添加市字段
    console.log('📋 添加市字段');
    const cityField = await client.createField('towns', {
      name: 'city',
      type: 'string',
      interface: 'input',
      uiSchema: {
        type: 'string',
        title: '市',
        'x-component': 'Input',
        required: true
      }
    });
    console.log(`✅ 成功创建市字段: ${cityField.name}`);

    // 添加区字段
    console.log('📋 添加区字段');
    const districtField = await client.createField('towns', {
      name: 'district',
      type: 'string',
      interface: 'input',
      uiSchema: {
        type: 'string',
        title: '区',
        'x-component': 'Input',
        required: true
      }
    });
    console.log(`✅ 成功创建区字段: ${districtField.name}`);

    // 添加名称字段
    console.log('📋 添加名称字段');
    const nameField = await client.createField('towns', {
      name: 'name',
      type: 'string',
      interface: 'input',
      uiSchema: {
        type: 'string',
        title: '名称',
        'x-component': 'Input',
        required: true
      }
    });
    console.log(`✅ 成功创建名称字段: ${nameField.name}`);
    console.log();

    // 创建一些示例数据
    console.log('📋 创建示例镇街数据');
    const town1 = await client.createRecord('towns', {
      city: '广州市',
      district: '天河区',
      name: '珠江新城街道'
    });
    console.log(`✅ 创建镇街记录: ${town1.city} ${town1.district} ${town1.name}`);

    const town2 = await client.createRecord('towns', {
      city: '广州市',
      district: '越秀区',
      name: '北京街道'
    });
    console.log(`✅ 创建镇街记录: ${town2.city} ${town2.district} ${town2.name}`);

    const town3 = await client.createRecord('towns', {
      city: '深圳市',
      district: '南山区',
      name: '南头街道'
    });
    console.log(`✅ 创建镇街记录: ${town3.city} ${town3.district} ${town3.name}`);

    console.log();
    console.log('🎉 镇街集合创建完成！');

  } catch (error) {
    if (error.message.includes('already exists') || error.message.includes('duplicate')) {
      console.log('ℹ️  镇街集合已存在，跳过创建');
    } else {
      console.error('❌ 创建镇街集合失败:', error.message);
    }
  }
}

// 运行创建脚本
createTownsCollection().catch(console.error);

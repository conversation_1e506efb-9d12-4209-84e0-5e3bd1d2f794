/**
 * 测试修复后的区块创建功能
 */

const { NocoBaseClient } = require('./mcp-server-nocobase/dist/client.js');
const { BLOCK_TEMPLATES } = require('./mcp-server-nocobase/dist/block-templates.js');

async function testFixedBlockCreation() {
  console.log('🧪 Testing fixed block creation functionality...\n');

  // 初始化客户端
  const client = new NocoBaseClient({
    baseUrl: 'https://app.dev.orb.local/api',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ1MjY4NDAsImV4cCI6MzMzMTIxMjY4NDB9.jub9QcFtDdjFrn3UzrT6PxNA19ZuHWGqIHfcBjdNhQA',
    app: 'mcp_playground'
  });

  try {
    // 测试参数
    const parentUid = 'tab-1754717531467-kt42byc12'; // 我们创建的页面的 tab UID
    
    console.log('📋 Test Parameters:');
    console.log(`- Parent UID: ${parentUid}`);
    console.log(`- Collection: towns`);
    console.log('');

    // 测试 1: 创建表格区块
    console.log('🔧 Test 1: Creating Table Block...');
    try {
      const tableTemplate = BLOCK_TEMPLATES.table;
      const tableSchema = tableTemplate.createSchema({
        collectionName: 'towns',
        dataSource: 'main',
        title: 'Towns Table (Fixed)'
      });

      console.log('📝 Table Schema Generated:');
      console.log(JSON.stringify(tableSchema, null, 2));
      console.log('');

      const tableResult = await client.insertBlockToGrid(parentUid, tableSchema, 'beforeEnd');
      console.log('✅ Table block created successfully!');
      console.log('📊 Result:', JSON.stringify(tableResult, null, 2));
      console.log('');
    } catch (error) {
      console.error('❌ Table block creation failed:', error.message);
      console.error('📋 Error details:', error.response?.data || error);
      console.log('');
    }

    // 测试 2: 创建 Markdown 区块
    console.log('🔧 Test 2: Creating Markdown Block...');
    try {
      const markdownTemplate = BLOCK_TEMPLATES.markdown;
      const markdownSchema = markdownTemplate.createSchema({
        title: 'Test Markdown (Fixed)',
        content: '# 修复测试\n\n这是一个测试修复后的 Markdown 区块创建功能。\n\n- ✅ API 端点已修复\n- ✅ 参数格式已修复\n- ✅ Schema 结构已验证'
      });

      console.log('📝 Markdown Schema Generated:');
      console.log(JSON.stringify(markdownSchema, null, 2));
      console.log('');

      const markdownResult = await client.insertBlockToGrid(parentUid, markdownSchema, 'beforeEnd');
      console.log('✅ Markdown block created successfully!');
      console.log('📊 Result:', JSON.stringify(markdownResult, null, 2));
      console.log('');
    } catch (error) {
      console.error('❌ Markdown block creation failed:', error.message);
      console.error('📋 Error details:', error.response?.data || error);
      console.log('');
    }

    // 测试 3: 创建图表区块
    console.log('🔧 Test 3: Creating Chart Block...');
    try {
      const chartTemplate = BLOCK_TEMPLATES.chart;
      const chartSchema = chartTemplate.createSchema({
        collectionName: 'towns',
        dataSource: 'main',
        title: 'Towns Chart (Fixed)',
        chartType: 'bar',
        config: {
          xField: 'name',
          yField: 'id',
          aggregation: 'count'
        }
      });

      console.log('📝 Chart Schema Generated:');
      console.log(JSON.stringify(chartSchema, null, 2));
      console.log('');

      const chartResult = await client.insertBlockToGrid(parentUid, chartSchema, 'beforeEnd');
      console.log('✅ Chart block created successfully!');
      console.log('📊 Result:', JSON.stringify(chartResult, null, 2));
      console.log('');
    } catch (error) {
      console.error('❌ Chart block creation failed:', error.message);
      console.error('📋 Error details:', error.response?.data || error);
      console.log('');
    }

    console.log('🎉 Block creation tests completed!');

  } catch (error) {
    console.error('💥 Test setup failed:', error.message);
    console.error('📋 Error details:', error);
  }
}

// 运行测试
testFixedBlockCreation().catch(console.error);

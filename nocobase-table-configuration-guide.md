# NocoBase 表格区块完整配置指南

## 🎯 概述

通过 Playwright 观察和 API 测试，我们深入了解了 NocoBase 表格区块的完整配置选项。本指南提供了创建功能完整的表格区块的详细说明。

## 📊 表格区块的核心组成

### 1. 基础结构
```javascript
{
  type: 'void',
  'x-decorator': 'TableBlockProvider',
  'x-component': 'CardItem',
  'x-settings': 'blockSettings:table',
  properties: {
    actions: { /* 表格级别操作 */ },
    [tableUid]: { /* 表格主体 */ }
  }
}
```

### 2. 表格级别操作 (Table Actions)

通过 Playwright 观察到的可用操作：

- **Filter** (过滤器) - `x-component: 'Filter.Action'`
- **Add new** (添加新记录) - `x-action: 'create'`
- **Popup** (弹窗)
- **Delete** (删除)
- **Refresh** (刷新) - `x-action: 'refresh'`
- **Link** (链接)
- **Custom request** (自定义请求)
- **Bulk edit** (批量编辑)
- **Bulk update** (批量更新)
- **Export** (导出)
- **Import** (导入)

#### 添加新记录操作示例
```javascript
{
  type: 'void',
  'x-action': 'create',
  'x-decorator': 'ACLActionProvider',
  'x-component': 'Action',
  'x-settings': 'actionSettings:addNew',
  'x-component-props': {
    openMode: 'drawer',
    type: 'primary',
    icon: 'PlusOutlined'
  },
  title: '{{t("Add new")}}',
  properties: {
    drawer: {
      type: 'void',
      'x-component': 'Action.Container',
      properties: {
        tabs: {
          'x-component': 'Tabs',
          'x-initializer': 'TabPaneInitializers'
        }
      }
    }
  }
}
```

### 3. 数据列配置

通过 "Configure columns" 观察到的可用字段：

- **Display collection fields** (显示集合字段)
  - City (城市)
  - District (区县)
  - Town Name (镇街名称)
  - ID (ID)
  - Created at (创建时间)
  - Last updated at (最后更新时间)
  - Created by (创建者)
  - Last updated by (最后更新者)

#### 数据列结构
```javascript
{
  type: 'void',
  'x-decorator': 'TableV2.Column.Decorator',
  'x-component': 'TableV2.Column',
  'x-designer': 'TableV2.Column.Designer',
  title: '{{t("Field Name")}}',
  properties: {
    [fieldName]: {
      'x-collection-field': `${collectionName}.${fieldName}`,
      'x-component': 'CollectionField',
      'x-read-pretty': true
    }
  }
}
```

### 4. 行操作 (Row Actions)

通过 "Configure actions" 观察到的可用行操作：

- **View** (查看) - `x-action: 'view'`
- **Edit** (编辑) - `x-action: 'update'`
- **Delete** (删除) - `x-action: 'destroy'`
- **Popup** (弹窗)
- **Update record** (更新记录)
- **Custom request** (自定义请求)
- **Link** (链接)
- **Duplicate** (复制)

#### Actions 列结构
```javascript
{
  type: 'void',
  'x-decorator': 'TableV2.Column.ActionBar',
  'x-component': 'TableV2.Column',
  'x-designer': 'TableV2.ActionColumnDesigner',
  'x-initializer': 'table:configureItemActions',
  title: '{{t("Actions")}}',
  properties: {
    [actionUid]: {
      type: 'void',
      'x-action': 'view',
      'x-component': 'Action.Link',
      'x-decorator': 'ACLActionProvider',
      'x-settings': 'actionSettings:view',
      title: '{{t("View")}}',
      properties: {
        drawer: {
          'x-component': 'Action.Container',
          properties: {
            tabs: {
              'x-component': 'Tabs',
              'x-initializer': 'TabPaneInitializers'
            }
          }
        }
      }
    }
  }
}
```

## 🛠️ 实际观察到的表格配置

### 成功配置的表格特征

1. **标题**: "Towns" (自动从集合名称生成)
2. **表格操作**: 
   - ✅ Add new (添加新记录)
   - ✅ Configure actions (配置操作)
3. **数据列**:
   - ✅ Actions (操作列)
   - ✅ Town Name (镇街名称)
   - ✅ City (城市)
   - ✅ District (区县)
4. **行操作**:
   - ✅ View (查看详情)
5. **数据显示**:
   - 珠江新城街道 | 广州市 | 天河区
   - 北京街道 | 广州市 | 越秀区
   - 南头街道 | 深圳市 | 南山区
   - Shekou Street | Shenzhen | Nanshan District

### 关键配置要素

1. **初始化器 (Initializers)**:
   - `table:configureActions` - 配置表格操作
   - `table:configureColumns` - 配置表格列
   - `table:configureItemActions` - 配置行操作

2. **设置 (Settings)**:
   - `blockSettings:table` - 表格区块设置
   - `actionSettings:addNew` - 添加操作设置
   - `actionSettings:view` - 查看操作设置
   - `fieldSettings:TableColumn` - 列字段设置

3. **组件 (Components)**:
   - `TableBlockProvider` - 表格区块提供者
   - `CardItem` - 卡片容器
   - `ActionBar` - 操作栏
   - `TableV2` - 表格组件
   - `TableV2.Column` - 表格列

## 📝 最佳实践

1. **使用正确的初始化器**: 确保使用 `table:configureActions` 和 `table:configureColumns`
2. **设置适当的权限**: 使用 `x-acl-action` 设置访问控制
3. **配置弹窗**: 为操作配置适当的弹窗和表单
4. **字段映射**: 确保 `x-collection-field` 正确映射到集合字段
5. **响应式设计**: 设置适当的列宽和固定位置

## 🎉 总结

通过深入分析 NocoBase 的表格配置，我们了解了：

- 表格区块的完整结构和配置选项
- 如何添加表格级别和行级别的操作
- 如何配置数据列和字段显示
- 如何设置弹窗和交互行为
- 实际的配置模式和最佳实践

这些知识为创建功能完整的 NocoBase 表格区块提供了坚实的基础。

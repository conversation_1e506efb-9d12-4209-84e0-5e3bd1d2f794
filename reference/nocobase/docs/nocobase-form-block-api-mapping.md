# NocoBase Form Block 操作与 API 参数对应关系

本文档详细描述了 NocoBase 中 Form Block 的各种操作与其对应的 API 调用参数，为开发者提供完整的参考指南。

## 目录

1. [Form Block 架构概述](#1-form-block-架构概述)
2. [基础表单操作](#2-基础表单操作)
3. [表单数据处理机制](#3-表单数据处理机制)
4. [表单提交流程](#4-表单提交流程)
5. [高级表单操作](#5-高级表单操作)
6. [前端组件配置示例](#6-前端组件配置示例)
7. [参数传递机制](#7-参数传递机制)

---

## 1. Form Block 架构概述

### 核心组件结构

```
FormBlockProvider (表单上下文提供者)
├── CardItem (容器组件)
│   ├── FormV2 (主表单组件)
│   │   ├── Grid (表单字段容器)
│   │   │   └── Form Fields (动态表单字段)
│   │   └── ActionBar (表单操作栏)
│   │       └── Submit/Cancel Actions (提交/取消操作)
└── Block Settings & Configuration (区块配置)
```

### 关键文件位置

- **FormBlockProvider**: `/packages/core/client/src/block-provider/FormBlockProvider.tsx`
- **FormV2 Component**: `/packages/core/client/src/schema-component/antd/form-v2/Form.tsx`
- **Action Component**: `/packages/core/client/src/schema-component/antd/action/Action.tsx`
- **Form Actions**: `/packages/core/client/src/block-provider/hooks/index.ts:237-1086`

---

## 2. 基础表单操作

### Create Form (创建表单)

- **x-action**: `submit` (配合 `useCreateActionProps`)
- **x-use-component-props**: `useCreateActionProps`
- **API 参数**:
  ```typescript
  {
    values: object,                    // 表单数据
    filterByTk?: string | number,      // 主键值 (编辑时)
    filterKeys?: string[],             // 过滤字段
    updateAssociationValues?: boolean,  // 是否更新关联值
    triggerWorkflows?: string,          // 触发工作流
    overwriteValues?: object,          // 覆盖值
    assignedValues?: object,           // 分配值
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:237`

**实现逻辑**:
```typescript
export const useCreateActionProps = () => {
  const collectValues = useCollectValuesToSubmit();
  const action = record.isNew ? actionField.componentProps.saveMode || 'create' : 'update';
  
  return {
    async onClick() {
      const values = await collectValues();
      const data = await resource[action]({
        values,
        filterKeys: filterKeys,
        filterByTk,
        triggerWorkflows: triggerWorkflows?.length
          ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
          : undefined,
        updateAssociationValues,
      });
    }
  };
};
```

### Update Form (更新表单)

- **x-action**: `submit` (配合 `useUpdateActionProps`)
- **x-use-component-props**: `useUpdateActionProps`
- **API 参数**:
  ```typescript
  {
    filterByTk: string | number,       // 主键值 (必需)
    values: object,                   // 更新的表单数据
    overwriteValues?: object,         // 覆盖值
    assignedValues?: object,          // 分配值
    updateAssociationValues?: boolean, // 是否更新关联值
    triggerWorkflows?: string,         // 触发工作流
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:948`

**实现逻辑**:
```typescript
export const useUpdateActionProps = () => {
  return {
    async onClick() {
      const values = getFormValues({
        filterByTk, field, form, fieldNames, getField, resource,
        actionFields: getActiveFieldsName?.('form') || [],
      });
      
      const result = await resource.update({
        filterByTk,
        values: {
          ...values,
          ...overwriteValues,
          ...assignedValues,
        },
        updateAssociationValues,
        triggerWorkflows: triggerWorkflows?.length
          ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
          : undefined,
      });
    }
  };
};
```

### Custom Form Request (自定义表单请求)

- **x-action**: `customize:form:request`
- **x-use-component-props**: `useCustomizeRequestActionProps`
- **API 参数**:
  ```typescript
  {
    url: string,                      // 请求地址
    method: 'POST' | 'PUT' | 'PATCH', // 请求方法
    headers?: object,                 // 请求头
    params?: object,                  // URL 参数
    data?: object,                    // 请求体数据
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:844`

---

## 3. 表单数据处理机制

### 数据收集流程

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:163`

```typescript
export function useCollectValuesToSubmit() {
  return useCallback(async () => {
    const { assignedValues: originalAssignedValues = {}, overwriteValues } = actionSchema?.['x-action-settings'] ?? {};
    const values = getFormValues({
      filterByTk, field, form, fieldNames, getField, resource,
      actionFields: getActiveFieldsName?.('form') || [],
    });

    // 处理分配值
    const assignedValues = {};
    const waitList = Object.keys(originalAssignedValues).map(async (key) => {
      const value = originalAssignedValues[key];
      const collectionField = getField(key);
      
      if (isVariable(value)) {
        const { value: parsedValue } = (await variables?.parseVariable(value, localVariables)) || {};
        assignedValues[key] = transformVariableValue(parsedValue, { targetCollectionField: collectionField });
      } else if (value !== '') {
        assignedValues[key] = value;
      }
    });
    await Promise.all(waitList);

    return {
      ...values,
      ...overwriteValues,
      ...assignedValues,
    };
  }, [/* dependencies */]);
}
```

### 表单值过滤

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:101`

```typescript
function getFilteredFormValues(form) {
  const values = _.cloneDeep(form.values);
  const allFields = [];
  form.query('*').forEach((field) => {
    if (field) {
      allFields.push(field);
    }
  });
  
  // 过滤只读字段
  const readonlyPaths = _.uniq(
    allFields
      .filter((field) => {
        const segments = field.path?.segments || [];
        const path = segments.length <= 1 ? segments.join('.') : segments.slice(0, -1).join('.');
        return field?.componentProps?.readOnlySubmit && !get(values, path)[field?.componentProps.filterTargetKey];
      })
      .map((field) => {
        const segments = field.path?.segments || [];
        if (segments.length <= 1) {
          return segments.join('.');
        }
        return segments.slice(0, -1).join('.');
      }),
  );
  
  readonlyPaths.forEach((path, index) => {
    if ((index !== 0 || path.includes('.')) && !values[path]) {
      _.unset(values, path);
    }
  });
  
  return values;
}
```

### 变量处理机制

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:188`

```typescript
// 处理分配值中的变量
const waitList = Object.keys(originalAssignedValues).map(async (key) => {
  const value = originalAssignedValues[key];
  const collectionField = getField(key);
  
  if (isVariable(value)) {
    const { value: parsedValue } = (await variables?.parseVariable(value, localVariables)) || {};
    assignedValues[key] = transformVariableValue(parsedValue, { targetCollectionField: collectionField });
  } else if (value !== '') {
    assignedValues[key] = value;
  }
});
```

---

## 4. 表单提交流程

### 完整提交流程

```typescript
async function formSubmission() {
  // 1. 表单验证 (可选跳过)
  if (!skipValidator) {
    await form.submit();
  }
  
  // 2. 收集表单数据
  const values = await collectValues();
  
  // 3. 设置加载状态
  actionField.data.loading = true;
  
  try {
    // 4. 执行 API 请求
    const data = await resource[action]({
      values,
      filterByTk,
      filterKeys,
      updateAssociationValues,
      triggerWorkflows,
    });
    
    // 5. 处理成功响应
    actionField.data.loading = false;
    actionField.data.data = data;
    
    // 6. 处理后续操作
    if (actionAfterSuccess === 'previous') {
      setVisible?.(false);
    }
    
    // 7. 显示成功消息
    message.success(compile(successMessage) || t('Saved successfully'));
    
    // 8. 处理页面跳转
    if (redirectTo) {
      if (isURL(redirectTo)) {
        window.location.href = redirectTo;
      } else {
        navigate(redirectTo);
      }
    }
    
  } catch (error) {
    // 9. 错误处理
    actionField.data.loading = false;
    console.error('Form submission failed:', error);
  }
}
```

### 提交配置参数

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:258`

```typescript
const {
  manualClose,                    // 手动关闭弹窗
  redirecting,                    // 是否跳转
  redirectTo: rawRedirectTo,      // 跳转地址
  successMessage,                 // 成功消息
  actionAfterSuccess,             // 成功后操作
} = onSuccess || {};
```

---

## 5. 高级表单操作

### Bulk Update (批量更新)

- **x-action**: `customize:bulk:update`
- **x-use-component-props**: `useCustomizeBulkUpdateActionProps`
- **API 参数**:
  ```typescript
  {
    values: object,         // 更新的数据
    filter?: object,       // 过滤条件
    forceUpdate: boolean,   // 强制更新
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:725`

### Association Create (关联创建)

- **x-action**: `submit` (配合 `useAssociationCreateActionProps`)
- **x-use-component-props**: `useAssociationCreateActionProps`
- **API 参数**:
  ```typescript
  {
    values: object,                    // 关联数据
    filterKeys?: string[],             // 过滤字段
    triggerWorkflows?: string,          // 触发工作流
    overwriteValues?: object,          // 覆盖值
    assignedValues?: object,           // 分配值
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:345`

### Filter Form (筛选表单)

- **x-action**: `filter`
- **x-use-component-props**: `useFilterBlockActionProps`
- **API 参数**:
  ```typescript
  {
    filter: object,         // 筛选条件
    page: number,           // 页码
    pageSize: number,       // 每页条数
  }
  ```

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:567`

---

## 6. 前端组件配置示例

### 创建表单配置

```typescript
{
  type: 'void',
  'x-decorator': 'FormBlockProvider',
  'x-use-decorator-props': 'useCreateFormBlockDecoratorProps',
  'x-component': 'CardItem',
  'x-settings': 'blockSettings:createForm',
  properties: {
    form: {
      'x-component': 'FormV2',
      'x-use-component-props': 'useCreateFormBlockProps',
      properties: {
        grid: {
          'x-component': 'Grid',
          'x-initializer': 'form:configureFields',
          properties: {
            // 表单字段配置
            fieldName: {
              type: 'string',
              title: '字段名称',
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              required: true,
            },
          },
        },
        actions: {
          'x-component': 'ActionBar',
          'x-initializer': 'createForm:configureActions',
          properties: {
            submit: {
              type: 'void',
              title: '提交',
              'x-action': 'submit',
              'x-component': 'Action',
              'x-use-component-props': 'useCreateActionProps',
              'x-component-props': {
                type: 'primary',
                htmlType: 'submit',
              },
              'x-settings': 'actionSettings:createSubmit',
            },
            cancel: {
              type: 'void',
              title: '取消',
              'x-component': 'Action',
              'x-component-props': {
                useProps: '{{ useCancelActionProps }}',
              },
            },
          },
        },
      },
    },
  },
}
```

### 编辑表单配置

```typescript
{
  type: 'void',
  'x-decorator': 'FormBlockProvider',
  'x-use-decorator-props': 'useUpdateFormBlockDecoratorProps',
  'x-component': 'CardItem',
  'x-settings': 'blockSettings:updateForm',
  properties: {
    form: {
      'x-component': 'FormV2',
      'x-use-component-props': 'useUpdateFormBlockProps',
      properties: {
        grid: {
          'x-component': 'Grid',
          'x-initializer': 'form:configureFields',
        },
        actions: {
          'x-component': 'ActionBar',
          'x-initializer': 'updateForm:configureActions',
          properties: {
            submit: {
              type: 'void',
              title: '提交',
              'x-action': 'submit',
              'x-component': 'Action',
              'x-use-component-props': 'useUpdateActionProps',
              'x-settings': 'actionSettings:updateSubmit',
            },
          },
        },
      },
    },
  },
}
```

### 自定义操作配置

```typescript
{
  type: 'void',
  title: '自定义操作',
  'x-action': 'customize:form:request',
  'x-component': 'Action',
  'x-use-component-props': 'useCustomizeRequestActionProps',
  'x-settings': 'actionSettings:customizeRequest',
  'x-action-settings': {
    requestSettings: {
      url: '/api/custom/endpoint',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      data: {
        // 请求数据模板
      },
    },
    onSuccess: {
      successMessage: '操作成功',
      redirecting: true,
      redirectTo: '/success-page',
    },
    skipValidator: false,
  },
}
```

---

## 7. 参数传递机制

### 表单参数获取流程

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:26-38`

```typescript
export function getRepositoryFromParams(ctx: Context) {
  const { resourceName, sourceId, actionName } = ctx.action;

  if (sourceId === '_' && ['get', 'list'].includes(actionName)) {
    const collection = ctx.db.getCollection(resourceName);
    return ctx.db.getRepository<Repository>(collection.name);
  }

  if (sourceId) {
    return ctx.db.getRepository<MultipleRelationRepository>(resourceName, sourceId);
  }

  return ctx.db.getRepository<Repository>(resourceName);
}
```

### Action Hooks 参数映射

| Hook | Action Type | API Method | Key Parameters |
|------|-------------|------------|----------------|
| `useCreateActionProps` | `create` | `resource.create()` | `values`, `filterKeys`, `updateAssociationValues` |
| `useUpdateActionProps` | `update` | `resource.update()` | `filterByTk`, `values`, `updateAssociationValues` |
| `useCustomizeRequestActionProps` | `customize:form:request` | `apiClient.request()` | `url`, `method`, `headers`, `data` |
| `useCustomizeBulkUpdateActionProps` | `customize:bulk:update` | `resource.update()` | `values`, `filter`, `forceUpdate` |
| `useAssociationCreateActionProps` | `create` | `resource.create()` | `values`, `filterKeys`, `triggerWorkflows` |

### 变量系统参数

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:198`

```typescript
// 变量解析
if (isVariable(value)) {
  const { value: parsedValue } = (await variables?.parseVariable(value, localVariables)) || {};
  assignedValues[key] = transformVariableValue(parsedValue, { targetCollectionField: collectionField });
}

// 变量类型
type VariableOption = {
  name: string;
  ctx: any;
  collectionName?: string;
};
```

### 工作流触发参数

**源码位置**: `/packages/core/client/src/block-provider/hooks/index.ts:279`

```typescript
// 工作流触发参数格式
triggerWorkflows: triggerWorkflows?.length
  ? triggerWorkflows.map((row) => [row.workflowKey, row.context].filter(Boolean).join('!')).join(',')
  : undefined,

// 示例: "workflow1!context1,workflow2!context2"
```

---

## 总结

本文档详细描述了 NocoBase Form Block 的完整操作体系，包括：

1. **基础表单操作**: 创建、更新、自定义请求
2. **数据处理机制**: 数据收集、过滤、变量处理
3. **表单提交流程**: 验证、提交、成功处理、错误处理
4. **高级操作**: 批量更新、关联创建、筛选表单
5. **前端配置**: 表单区块配置、操作配置
6. **参数传递**: 从前端到后端的完整数据流

所有代码示例都基于 NocoBase 最新源码，确保了技术准确性和实用性。开发者可以基于这份文档准确理解和使用 NocoBase 的 Form Block 功能。

---

**文档版本**: v1.0  
**更新日期**: 2024-08-09  
**NocoBase 版本**: 基于最新源码分析